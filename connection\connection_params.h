#ifndef DATABASE_CONNECTION_PARAMS_H
#define DATABASE_CONNECTION_PARAMS_H

#include <string>
#include <string_view>
#include <chrono>
#include <unordered_map>
#include <vector>

namespace database {

/**
 * @brief Metadata about a database connection
 *
 * This class provides information about a database connection,
 * such as the database product name, version, and supported features.
 */
class ConnectionMetadata {
public:
    /**
     * @brief Constructor
     * @param productName The database product name
     * @param productVersion The database product version
     */
    ConnectionMetadata(
        std::string_view productName = "",
        std::string_view productVersion = "")
        : m_productName{productName},
        m_productVersion{productVersion} {
    }

    /**
     * @brief Get the database product name
     * @return The database product name
     */
    [[nodiscard]] std::string_view getProductName() const { return m_productName; }

    /**
     * @brief Set the database product name
     * @param name The database product name
     */
    void setProductName(std::string_view name) { m_productName = name; }

    /**
     * @brief Get the database product version
     * @return The database product version
     */
    [[nodiscard]] std::string_view getProductVersion() const { return m_productVersion; }

    /**
     * @brief Set the database product version
     * @param version The database product version
     */
    void setProductVersion(std::string_view version) { m_productVersion = version; }

    /**
     * @brief Get the major version
     * @return The major version
     */
    int getMajorVersion() const { return m_majorVersion; }

    /**
     * @brief Set the major version
     * @param version The major version
     */
    void setMajorVersion(int version) { m_majorVersion = version; }

    /**
     * @brief Get the minor version
     * @return The minor version
     */
    int getMinorVersion() const { return m_minorVersion; }

    /**
     * @brief Set the minor version
     * @param version The minor version
     */
    void setMinorVersion(int version) { m_minorVersion = version; }

    /**
     * @brief Get the user name
     * @return The user name
     */
    [[nodiscard]] std::string_view getUserName() const { return m_userName; }

    /**
     * @brief Set the user name
     * @param name The user name
     */
    void setUserName(std::string_view name) { m_userName = name; }

    /**
     * @brief Get the URL
     * @return The URL
     */
    [[nodiscard]] std::string_view getURL() const { return m_url; }

    /**
     * @brief Set the URL
     * @param url The URL
     */
    void setURL(std::string_view url) { m_url = url; }

    /**
     * @brief Check if transactions are supported
     * @return True if supported, false otherwise
     */
    bool supportsTransactions() const { return m_supportsTransactions; }

    /**
     * @brief Set whether transactions are supported
     * @param supported True if supported, false otherwise
     */
    void setSupportsTransactions(bool supported) { m_supportsTransactions = supported; }

    /**
     * @brief Check if savepoints are supported
     * @return True if supported, false otherwise
     */
    bool supportsSavepoints() const { return m_supportsSavepoints; }

    /**
     * @brief Set whether savepoints are supported
     * @param supported True if supported, false otherwise
     */
    void setSupportsSavepoints(bool supported) { m_supportsSavepoints = supported; }

    /**
     * @brief Check if batch updates are supported
     * @return True if supported, false otherwise
     */
    bool supportsBatchUpdates() const { return m_supportsBatchUpdates; }

    /**
     * @brief Set whether batch updates are supported
     * @param supported True if supported, false otherwise
     */
    void setSupportsBatchUpdates(bool supported) { m_supportsBatchUpdates = supported; }

    /**
     * @brief Get the maximum number of connections
     * @return The maximum number of connections
     */
    int getMaxConnections() const { return m_maxConnections; }

    /**
     * @brief Set the maximum number of connections
     * @param max The maximum number of connections
     */
    void setMaxConnections(int max) { m_maxConnections = max; }

    /**
     * @brief Get the list of supported SQL keywords
     * @return The list of supported SQL keywords
     */
    const std::vector<std::string>& getSQLKeywords() const { return m_sqlKeywords; }

    /**
     * @brief Set the list of supported SQL keywords
     * @param keywords The list of supported SQL keywords
     */
    void setSQLKeywords(const std::vector<std::string>& keywords) { m_sqlKeywords = keywords; }

private:
    std::string m_productName;
    std::string m_productVersion;
    int m_majorVersion = 0;
    int m_minorVersion = 0;
    std::string m_userName;
    std::string m_url;
    bool m_supportsTransactions = false;
    bool m_supportsSavepoints = false;
    bool m_supportsBatchUpdates = false;
    int m_maxConnections = 0;
    std::vector<std::string> m_sqlKeywords;
};

/**
 * @brief Connection parameters for database connections
 *
 * This class encapsulates the parameters needed to establish a database connection.
 */
class ConnectionParams {
public:
    /**
     * @brief Default constructor
     */
    ConnectionParams() = default;

    /**
     * @brief Constructor with parameters
     * @param host The host name or IP address
     * @param port The port number
     * @param userName The userName
     * @param password The password
     * @param database The database name
     */
    ConnectionParams(
        std::string_view host,
        int port,
        std::string_view userName,
        std::string_view password,
        std::string_view database)
        : m_host{host},
        m_port{port},
        m_database{database},
        m_userName{userName},
        m_password{password} {}

    /**
     * @brief Constructor
     * @param driverName The name of the database driver
     */
    explicit ConnectionParams(std::string_view driverName)
        : m_driverName{driverName} {}

    /**
     * @brief Set the driver name
     * @param driverName The name of the database driver
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setDriverName(std::string_view driverName) {m_driverName = driverName; return *this; }

    /**
     * @brief Get the driver name
     * @return The driver name
     */
    [[nodiscard]] std::string_view driverName() const { return m_driverName; }

    /**
     * @brief Set the host
     * @param host The host name or IP address
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setHost(std::string_view host) { m_host = host; return *this; }

    /**
     * @brief Get the host
     * @return The host name or IP address
     */
    [[nodiscard]] std::string_view hostName() const { return m_host; }

    /**
     * @brief Set the port
     * @param port The port number
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setPort(int port) { m_port = port; return *this; }

    /**
     * @brief Get the port
     * @return The port number
     */
    [[nodiscard]] int port() const { return m_port; }

    /**
     * @brief Set the database name
     * @param database The database name
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setDatabase(std::string_view database) { m_database = database; return *this; }

    /**
     * @brief Get the database name
     * @return The database name
     */
    [[nodiscard]] std::string_view database() const { return m_database; }

    /**
     * @brief Set the userName
     * @param userName The userName
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setUserName(std::string_view userName) { m_userName = userName; return *this; }

    /**
     * @brief Get the userName
     * @return The userName
     */
    [[nodiscard]] std::string_view userName() const { return m_userName; }

    /**
     * @brief Set the password
     * @param password The password
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setPassword(std::string_view password) { m_password = password; return *this; }

    /**
     * @brief Get the password
     * @return The password
     */
    [[nodiscard]] std::string_view password() const { return m_password; }

    /**
     * @brief Set a connection option
     * @param name The option name
     * @param value The option value
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setOption(std::string_view name, std::string_view value) {
        m_options[std::string(name)] = std::string(value);
        return *this;
    }

    /**
     * @brief Remove a connection option
     * @param name The option name to remove
     * @return Reference to this object for method chaining
     */
    ConnectionParams& removeOption(std::string_view name) {
        m_options.erase(std::string(name));
        return *this;
    }

    /**
     * @brief Get a connection option
     * @param name The option name
     * @param defaultValue The default value to return if the option is not set
     * @return The option value, or the default value if not set
     */
    [[nodiscard]] std::string_view option(std::string_view name, std::string_view defaultValue = "") const {
        auto it = m_options.find(std::string(name));
        return (it != m_options.end()) ? it->second : defaultValue;
    }

    /**
     * @brief Check if a connection option exists
     * @param name The option name to check
     * @return True if the option exists, false otherwise
     */
    [[nodiscard]] bool hasOption(std::string_view name) const {
        auto it = m_options.find(std::string(name));
        return (it != m_options.end());
    }

    /**
     * @brief Clear all connection options
     * @return Reference to this object for method chaining
     */
    ConnectionParams& clearOptions() {
        m_options.clear();
        return *this;
    }

    /**
     * @brief Get all connection options
     * @return The connection options
     */
    const std::unordered_map<std::string, std::string>& options() const { return m_options; }

    // Connection pool parameters

    /**
     * @brief Set the initial pool size
     * @param size The initial pool size
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setInitialPoolSize(int size) { m_initialPoolSize = size; return *this; }

    /**
     * @brief Get the initial pool size
     * @return The initial pool size
     */
    int initialPoolSize() const { return m_initialPoolSize; }

    /**
     * @brief Set the maximum pool size
     * @param size The maximum pool size
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setMaxPoolSize(int size) { m_maxPoolSize = size; return *this; }

    /**
     * @brief Get the maximum pool size
     * @return The maximum pool size
     */
    int maxPoolSize() const { return m_maxPoolSize; }

    /**
     * @brief Set the connection timeout
     * @param timeout The connection timeout
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setConnectionTimeout(std::chrono::milliseconds timeout) { m_connectionTimeout = timeout; return *this; }

    /**
     * @brief Get the connection timeout
     * @return The connection timeout
     */
    std::chrono::milliseconds connectionTimeout() const { return m_connectionTimeout; }

    /**
     * @brief Set the idle timeout
     * @param timeout The idle timeout
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setIdleTimeout(std::chrono::milliseconds timeout) { m_idleTimeout = timeout; return *this; }

    /**
     * @brief Get the idle timeout
     * @return The idle timeout
     */
    std::chrono::milliseconds idleTimeout() const { return m_idleTimeout; }

    /**
     * @brief Set whether to test connections on borrow
     * @param test Whether to test connections on borrow
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setTestOnBorrow(bool test) { m_testOnBorrow = test; return *this; }

    /**
     * @brief Check if connections should be tested on borrow
     * @return True if connections should be tested on borrow, false otherwise
     */
    bool testOnBorrow() const { return m_testOnBorrow; }

    /**
     * @brief Set the validation query
     * @param query The validation query
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setValidationQuery(std::string_view query) { m_validationQuery = query; return *this; }

    /**
     * @brief Get the validation query
     * @return The validation query
     */
    [[nodiscard]] std::string_view validationQuery() const { return m_validationQuery; }

    /**
     * @brief Set whether to test connections on return
     * @param test Whether to test connections on return
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setTestOnReturn(bool test) { m_testOnReturn = test; return *this; }

    /**
     * @brief Check if connections should be tested on return
     * @return True if connections should be tested on return, false otherwise
     */
    bool testOnReturn() const { return m_testOnReturn; }

    /**
     * @brief Set whether to test connections while idle
     * @param test Whether to test connections while idle
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setTestWhileIdle(bool test) { m_testWhileIdle = test; return *this; }

    /**
     * @brief Check if connections should be tested while idle
     * @return True if connections should be tested while idle, false otherwise
     */
    bool testWhileIdle() const { return m_testWhileIdle; }

    /**
     * @brief Set the maximum number of connection retries
     * @param retries The maximum number of retries
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setMaxRetries(int retries) { m_maxRetries = retries; return *this; }

    /**
     * @brief Get the maximum number of connection retries
     * @return The maximum number of retries
     */
    int maxRetries() const { return m_maxRetries; }

    /**
     * @brief Set the delay between connection retries
     * @param delay The retry delay
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setRetryDelay(std::chrono::milliseconds delay) { m_retryDelay = delay; return *this; }

    /**
     * @brief Get the delay between connection retries
     * @return The retry delay
     */
    std::chrono::milliseconds retryDelay() const { return m_retryDelay; }

    /**
     * @brief Set the validation timeout
     * @param timeout The validation timeout
     * @return Reference to this object for method chaining
     */
    ConnectionParams& setValidationTimeout(std::chrono::milliseconds timeout) { m_validationTimeout = timeout; return *this; }

    /**
     * @brief Get the validation timeout
     * @return The validation timeout
     */
    std::chrono::milliseconds validationTimeout() const { return m_validationTimeout; }

    /**
     * @brief Serialize connection parameters to a string
     * @return A string representation of the connection parameters
     */
    [[nodiscard]] std::string toString() const;

    /**
     * @brief Create ConnectionParams from a connection string
     * @param connectionString The connection string to parse
     * @return A ConnectionParams object parsed from the string
     * @throws std::invalid_argument if the connection string format is invalid
     */
    [[nodiscard]] static ConnectionParams fromString(std::string_view connectionString);

private:
    std::string m_driverName;
    std::string m_host;
    int m_port = 0;
    std::string m_database;
    std::string m_userName;
    std::string m_password;
    std::unordered_map<std::string, std::string> m_options;

    // Connection pool parameters
    int m_initialPoolSize = 5;
    int m_maxPoolSize = 10;
    std::chrono::milliseconds m_connectionTimeout = std::chrono::milliseconds(5000);
    std::chrono::milliseconds m_idleTimeout = std::chrono::milliseconds(60000);
    bool m_testOnBorrow = true;
    bool m_testOnReturn = false;
    bool m_testWhileIdle = true;
    std::string m_validationQuery = "SELECT 1";
    int m_maxRetries = 3;
    std::chrono::milliseconds m_retryDelay = std::chrono::milliseconds(1000);
    std::chrono::milliseconds m_validationTimeout = std::chrono::milliseconds(1000);
};

} // namespace database

#endif // DATABASE_CONNECTION_PARAMS_H
