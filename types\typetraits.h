﻿#ifndef TYPETRAITS_H
#define TYPETRAITS_H

#include <memory>
#include <type_traits>
#include <string>
#include <vector>
#include <array>
#include <deque>
#include <list>
#include <forward_list>
#include <map>
#include <unordered_map>
#include <set>
#include <unordered_set>
#include <queue>
#include <stack>

template<typename T>
concept has_operator_equal_v = requires(T a, const T b) {
    { a = b };
};

template<typename T, typename = void>
struct has_operator_not : std::false_type {};

template<typename T>
struct has_operator_not<T, std::void_t<decltype(!std::declval<T>())>> : std::true_type {};

template<typename T>
static constexpr bool has_operator_not_v =  has_operator_not<T>::value;

template<typename T, typename = void>
struct is_dereferenceable : std::false_type {};

template<typename T>
struct is_dereferenceable<T, std::void_t<decltype(std::declval<T>().operator->())> >
    : std::true_type {};

template <typename T>
inline constexpr bool is_dereferenceable_v = is_dereferenceable<T>::value;

// #################### 智能指针 ####################
// 检测是否是std::shared_ptr
template <typename>
struct is_shared_ptr : std::false_type {};

template <typename T>
struct is_shared_ptr<std::shared_ptr<T>> : std::true_type {};

// 检测是否是std::unique_ptr
template <typename>
struct is_unique_ptr : std::false_type {};

template <typename T, typename D>
struct is_unique_ptr<std::unique_ptr<T, D>> : std::true_type {};

// 检测是否是std::weak_ptr
template <typename>
struct is_weak_ptr : std::false_type {};

template <typename T>
struct is_weak_ptr<std::weak_ptr<T>> : std::true_type {};

// 组合所有检测结果
template <typename T>
struct is_smart_pointer : std::disjunction<
                              is_shared_ptr<T>,
                              is_unique_ptr<T>,
                              is_weak_ptr<T>
                              > {};

template <typename T>
inline constexpr bool is_smart_pointer_v = is_smart_pointer<T>::value;

// #################### 序列容器 ####################
// vector
template<typename T>
struct is_vector : std::false_type {};

template<typename T, typename Alloc>
struct is_vector<std::vector<T, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_vector_v = is_vector<T>::value;

// array
template<typename T>
struct is_array : std::false_type {};

template<typename T, size_t N>
struct is_array<std::array<T, N>> : std::true_type {};

template<typename T>
static constexpr bool is_array_v = is_array<T>::value;

// deque
template<typename T>
struct is_deque : std::false_type {};

template<typename T, typename Alloc>
struct is_deque<std::deque<T, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_deque_v = is_deque<T>::value;

// list
template<typename T>
struct is_list : std::false_type {};

template<typename T, typename Alloc>
struct is_list<std::list<T, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_list_v = is_list<T>::value;

// forward_list
template<typename T>
struct is_forward_list : std::false_type {};

template<typename T, typename Alloc>
struct is_forward_list<std::forward_list<T, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_forward_list_v = is_forward_list<T>::value;

// #################### 关联容器 ####################
// map
template<typename T>
struct is_map : std::false_type {};

template<typename Key, typename T, typename Comp, typename Alloc>
struct is_map<std::map<Key, T, Comp, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_map_v = is_map<T>::value;
// multimap
template<typename T>
struct is_multimap : std::false_type {};

template<typename Key, typename T, typename Comp, typename Alloc>
struct is_multimap<std::multimap<Key, T, Comp, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_multimap_v = is_multimap<T>::value;

// unordered_map
template<typename T>
struct is_unordered_map : std::false_type {};

template<typename Key, typename T, typename Hash, typename KeyEqual, typename Alloc>
struct is_unordered_map<std::unordered_map<Key, T, Hash, KeyEqual, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_unordered_map_v = is_unordered_map<T>::value;

// unordered_multimap
template<typename T>
struct is_unordered_multimap : std::false_type {};

template<typename Key, typename T, typename Hash, typename KeyEqual, typename Alloc>
struct is_unordered_multimap<std::unordered_multimap<Key, T, Hash, KeyEqual, Alloc>> : std::true_type {};

template<typename T>
static constexpr bool is_unordered_multimap_v = is_unordered_multimap<T>::value;

// set
template<typename T>
struct is_set : std::false_type {};

template<typename T, typename Comp, typename Alloc>
struct is_set<std::set<T, Comp, Alloc>> : std::true_type  {};

template<typename T>
static constexpr bool is_set_v = is_set<T>::value;

// multiset
template<typename T>
struct is_multiset : std::false_type {};

template<typename T, typename Comp, typename Alloc>
struct is_multiset<std::multiset<T, Comp, Alloc>> : std::true_type  {};

template<typename T>
static constexpr bool is_multiset_v = is_multiset<T>::value;

// unordered_set
template<typename T>
struct is_unordered_set : std::false_type {};

template<typename T, typename Comp, typename Alloc>
struct is_unordered_set<std::unordered_set<T, Comp, Alloc>> : std::true_type  {};

template<typename T>
static constexpr bool is_unordered_set_v = is_unordered_set<T>::value;

// unordered_multiset
template<typename T>
struct is_unordered_multiset : std::false_type {};

template<typename T, typename Comp, typename Alloc>
struct is_unordered_multiset<std::unordered_multiset<T, Comp, Alloc>> : std::true_type  {};

template<typename T>
static constexpr bool is_unordered_multiset_v = is_unordered_multiset<T>::value;

// #################### 容器适配器 ####################
// queue
template<typename T>
struct is_queue : std::false_type {};

template<typename T, typename Container>
struct is_queue<std::queue<T, Container>> : std::true_type {};

template<typename T>
static constexpr bool is_queue_v = is_queue<T>::value;

// priority_queue
template<typename T>
struct is_priority_queue : std::false_type {};

template<typename T, typename Container, typename Compare>
struct is_priority_queue<std::priority_queue<T, Container, Compare>> : std::true_type {};

template<typename T>
static constexpr bool is_priority_queue_v = is_priority_queue<T>::value;

// stack
template<typename T>
struct is_stack : std::false_type {};

template<typename T, typename Container>
struct is_stack<std::stack<T, Container>> : std::true_type {};

template<typename T>
static constexpr bool is_stack_v = is_stack<T>::value;

// #################### 结构 ####################
// pair
template<typename T>
struct is_pair : std::false_type {};

template<typename T1, typename T2>
struct is_pair<std::pair<T1, T2>> : std::true_type  {};

template<typename T>
static constexpr bool is_pair_v = is_pair<T>::value;

template<typename T>
struct pair_traits;

template<typename T1, typename T2>
struct pair_traits<std::pair<T1, T2>> {
    using first_type = T1;
    using second_type = T2;
    static constexpr bool is_pair = true;
};

template<typename T>
struct pair_traits {
    using first_type = void;
    using second_type = void;
    static constexpr bool is_pair = false;
};

template <typename T>
constexpr bool is_character_v = std::is_same_v<T, char>
                                || std::is_same_v<T, signed char>
                                || std::is_same_v<T, unsigned char>
                                || std::is_same_v<T, wchar_t>
                                // || std::is_same_v<T, char8_t>
                                || std::is_same_v<T, char16_t>
                                || std::is_same_v<T, char32_t>;

template <typename T>
constexpr bool is_characters_v = std::is_same_v<T, const char*>
                                 || std::is_same_v<T, char*>
                                 || std::is_same_v<T, const signed char*>
                                 || std::is_same_v<T, signed char*>
                                 || std::is_same_v<T, const unsigned char*>
                                 || std::is_same_v<T, unsigned char*>
                                 || std::is_same_v<T, const wchar_t*>
                                 || std::is_same_v<T, wchar_t*>
                                 || std::is_same_v<T, const char16_t*>
                                 || std::is_same_v<T, char16_t*>
                                 || std::is_same_v<T, const char32_t*>
                                 || std::is_same_v<T, char32_t*>;

template <typename T>
constexpr bool is_string_v = is_characters_v<T>
                             || std::is_same_v<T, std::string>
                             || std::is_same_v<T, std::wstring>
                             || std::is_same_v<T, std::u8string>
                             || std::is_same_v<T, std::u16string>
                             || std::is_same_v<T, std::u32string>;

template <typename T>
constexpr bool is_sequence_container_v = is_vector_v<T>
                                         || is_array_v<T>
                                         || is_deque_v<T>
                                         || is_list_v<T>
                                         || is_forward_list_v<T>;

template <typename T>
constexpr bool is_associative_container_v = is_map_v<T>
                                            || is_multimap_v<T>
                                            || is_unordered_map_v<T>
                                            || is_unordered_multimap_v<T>
                                            || is_set_v<T>
                                            || is_multiset_v<T>
                                            || is_unordered_set_v<T>
                                            || is_unordered_multiset_v<T>;

template <typename T>
constexpr bool is_container_adapters_v = is_queue_v<T>
                                         || is_priority_queue_v<T>
                                         || is_stack_v<T>;


template <typename T>
constexpr bool is_container_v = is_sequence_container_v<T>
                                || is_associative_container_v<T>
                                || is_associative_container_v<T>;


template <typename T, std::size_t N>
struct array_type {
    using type = T;
    static constexpr std::size_t size = N;
};

template<typename T, std::size_t N>
using array_type_t = typename array_type<T, N>::type;

template<typename T>
struct vector_element_type;

template<typename T, typename Alloc>
struct vector_element_type<std::vector<T, Alloc>> {
    using type = T;
};

template<typename T>
using vector_element_t = typename vector_element_type<T>::type;

#endif // TYPETRAITS_H
