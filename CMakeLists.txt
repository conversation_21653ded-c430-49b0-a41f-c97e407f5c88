cmake_minimum_required(VERSION 3.14)
project(database_v8 VERSION 0.1.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Compiler flags
if(MSVC)
    add_compile_options(/W4 /permissive- /Zc:__cplusplus /EHsc)
    # Enable C++20 features
    add_compile_options(/std:c++20)
else()
    add_compile_options(-Wall -Wextra -Wpedantic -Werror=return-type)
    # Enable thread safety analysis if using Clang
    if(CMAKE_CXX_COMPILER_ID MATCHES "Clang")
        add_compile_options(-Wthread-safety)
    endif()
endif()

# Download and configure GoogleTest
include(FetchContent)
FetchContent_Declare(
  googletest
  GIT_REPOSITORY https://github.com/google/googletest.git
  GIT_TAG v1.14.0
)
# For Windows: Prevent overriding the parent project's compiler/linker settings
set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
FetchContent_MakeAvailable(googletest)

# FetchContent_MakeAvailable already adds the subdirectory

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/types
    ${CMAKE_CURRENT_SOURCE_DIR}/utils
)

# Source files
file(GLOB_RECURSE SOURCES
    "*.cpp"
    "connection/*.cpp"
    "driver/*.cpp"
    "driver/sqlite/*.cpp"
    "exception/*.cpp"
)

# Exclude test files from the main library
list(FILTER SOURCES EXCLUDE REGEX ".*test.*\\.cpp$")
list(FILTER SOURCES EXCLUDE REGEX ".*Test.*\\.cpp$")
list(FILTER SOURCES EXCLUDE REGEX ".*third_party.*\\.cpp$")
list(FILTER SOURCES EXCLUDE REGEX ".*reference.*\\.cpp$")
list(FILTER SOURCES EXCLUDE REGEX ".*examples.*\\.cpp$")

# Create the main library
add_library(database_lib STATIC ${SOURCES})

target_include_directories(database_lib PRIVATE
    third_party/sqlite
)
target_link_directories(database_lib PRIVATE
    third_party/sqlite
)
target_link_libraries(database_lib PRIVATE ${CMAKE_CURRENT_SOURCE_DIR}/third_party/sqlite/sqlite3.lib)

# Enable testing
enable_testing()

# Add tests directory
add_subdirectory(tests)

# SQLite DLL will be copied in the tests/CMakeLists.txt file
