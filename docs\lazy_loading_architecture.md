# 延迟加载架构设计

## 🎯 设计目标

重新设计数据库抽象层，将SqlTable、SqlColumn、SqlIndex作为核心的数据库概念描述类，隐藏Metadata实现细节，通过延迟加载优化性能，支持不同的使用场景。

## 📋 核心设计原则

### 1. 核心类为主导
- **SqlTable**: 数据库表的核心表示，集成元数据访问
- **SqlColumn**: 数据库列的核心表示，支持类型和约束信息
- **SqlIndex**: 数据库索引的核心表示，支持索引属性和SQL生成

### 2. 元数据隐藏
- **SqlTableMetadata/SqlColumnMetadata/SqlIndexMetadata**: 作为内部数据存储
- 用户主要通过核心类访问功能，必要时才访问底层元数据
- 提供 `metadata()` 方法用于高级场景

### 3. 延迟加载优化
- **按需加载**: 只在需要时从数据库加载元数据
- **缓存机制**: 加载后缓存结果，避免重复查询
- **性能优化**: 支持不同使用模式的性能需求

## 🏗️ 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    用户API层                                 │
│  SqlTable, SqlColumn, SqlIndex (核心类)                     │
├─────────────────────────────────────────────────────────────┤
│                   延迟加载层                                 │
│  loadMetadata(), ensureMetadataLoaded(), 缓存管理           │
├─────────────────────────────────────────────────────────────┤
│                   元数据存储层                               │
│  SqlTableMetadata, SqlColumnMetadata, SqlIndexMetadata     │
├─────────────────────────────────────────────────────────────┤
│                   驱动接口层                                 │
│  SqlDriver, getTableMetadata(), getIndexMetadata()         │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 延迟加载机制

### 加载时机
1. **创建对象**: 不触发数据库查询
2. **基本操作**: 名称、模式等基本属性访问不需要元数据
3. **存在性检查**: 简单查询，不加载完整元数据
4. **详细信息**: 首次访问列、索引、类型等信息时触发加载
5. **缓存使用**: 后续访问使用缓存，不再查询数据库

### 缓存策略
```cpp
class SqlTable {
private:
    mutable bool m_metadataLoaded = false;
    mutable std::unique_ptr<SqlTableMetadata> m_metadata;
    mutable std::vector<SqlColumn> m_columns;
    mutable std::vector<SqlIndex> m_indexes;
    
    void ensureMetadataLoaded() const {
        if (!m_metadataLoaded) {
            loadMetadata();
        }
    }
};
```

## 📊 使用场景优化

### 1. 轻量级使用（SQL构建）
```cpp
// 只需要表名，不加载元数据
SqlTable users = driver->table("users");
std::string sql = users.selectSql({"id", "name"});
// 性能：极快，无数据库查询
```

### 2. 存在性检查
```cpp
// 简单查询，不加载完整元数据
bool exists = users.exists();
// 性能：快，单次简单查询
```

### 3. 详细信息访问
```cpp
// 首次访问触发完整元数据加载
const auto& columns = users.columns();
for (const auto& col : columns) {
    std::cout << col.name() << ": " << col.nativeTypeName() << std::endl;
}
// 性能：首次较慢，后续访问使用缓存
```

### 4. 预加载优化
```cpp
// 预加载所有元数据，适合需要大量访问的场景
users.preloadMetadata();
// 后续所有访问都使用缓存
```

## 🔧 API设计

### 核心类API
```cpp
class SqlTable {
public:
    // 基本属性（无延迟加载）
    const std::string& name() const;
    const std::string& schema() const;
    std::string qualifiedName() const;
    
    // 存在性检查（轻量级查询）
    bool exists() const;
    
    // 延迟加载属性
    std::optional<SqlObjectType> type() const;
    const std::string& comment() const;
    const std::vector<SqlColumn>& columns() const;
    const std::vector<SqlIndex>& indexes() const;
    
    // 缓存管理
    bool isMetadataLoaded() const;
    SqlTable& preloadMetadata();
    SqlTable& reload();
    
    // 高级访问
    const SqlTableMetadata* metadata() const;
};
```

### 驱动简化API
```cpp
class SqlDriver {
public:
    // 返回核心类对象，支持延迟加载
    std::vector<SqlTable> getTables() const;
    SqlTable table(std::string_view name) const;
    SqlIndex index(std::string_view name) const;
    
    // 简化的存在性检查
    bool tableExists(const SqlTable& table) const;
    bool indexExists(const SqlIndex& index) const;
    
    // 内部元数据API（用于延迟加载）
private:
    SqlTableMetadata getTableMetadata(const SqlTable& table) const;
    SqlIndexMetadata getIndexMetadata(const SqlIndex& index) const;
};
```

## ⚡ 性能特性

### 1. 零开销抽象
- 创建对象不触发数据库查询
- 基本操作（名称访问等）无性能损失
- 只在需要时才查询数据库

### 2. 智能缓存
- 一次加载，多次使用
- 自动缓存失效和重新加载
- 内存使用优化

### 3. 批量优化
```cpp
// 获取所有表，但不加载元数据
auto tables = driver->getTables();

// 只有在访问详细信息时才加载
for (const auto& table : tables) {
    if (table.name().starts_with("user_")) {
        // 只为匹配的表加载元数据
        const auto& columns = table.columns();
    }
}
```

## 🔍 使用示例

### 基本使用模式
```cpp
// 1. 轻量级表操作
SqlTable users = driver->table("users");
std::cout << "表名: " << users.name() << std::endl;  // 无查询

// 2. 存在性检查
if (users.exists()) {  // 轻量级查询
    std::cout << "表存在" << std::endl;
}

// 3. 详细信息访问
const auto& columns = users.columns();  // 触发元数据加载
for (const auto& col : columns) {
    std::cout << col.name() << ": " << col.type() << std::endl;
}

// 4. 后续访问使用缓存
const auto& indexes = users.indexes();  // 使用已加载的元数据
```

### 性能优化模式
```cpp
// 预加载模式
SqlTable users = driver->table("users");
users.preloadMetadata();  // 一次性加载所有元数据

// 批量处理模式
auto tables = driver->getTables();
for (auto& table : tables) {
    table.preloadMetadata();  // 预加载所有表的元数据
}

// 选择性加载模式
for (const auto& table : tables) {
    if (needsDetailedInfo(table.name())) {
        const auto& columns = table.columns();  // 只为需要的表加载
        processColumns(columns);
    }
}
```

## 🎯 优势总结

### 1. 性能优化
- **延迟加载**: 按需加载，避免不必要的数据库查询
- **缓存机制**: 一次加载，多次使用
- **零开销**: 基本操作无性能损失

### 2. 易用性
- **统一接口**: 通过核心类访问所有功能
- **隐藏复杂性**: 元数据细节对用户透明
- **灵活控制**: 支持预加载和手动缓存管理

### 3. 可扩展性
- **清晰分层**: 核心类、延迟加载、元数据存储分离
- **易于扩展**: 新功能可以无缝集成
- **向后兼容**: 保持API稳定性

### 4. 内存效率
- **按需分配**: 只为使用的对象分配内存
- **智能释放**: 支持缓存失效和重新加载
- **批量优化**: 支持批量操作的内存优化

这种设计实现了性能和易用性的完美平衡，既满足了轻量级使用场景的性能需求，又为复杂场景提供了完整的功能支持。 