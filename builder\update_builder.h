﻿#ifndef DATABASE_UPDATE_BUILDER_H
#define DATABASE_UPDATE_BUILDER_H

#include <vector>
#include <string>
#include <unordered_map>
#include <optional>

#include "sql_builder.h"
#include "sql_row.h"

namespace database {

/**
 * @brief Builder for SQL UPDATE queries
 *
 * This class provides a fluent interface for building SQL UPDATE queries
 * using object-based parameters. It supports updating specific columns
 * and adding WHERE conditions.
 */
class UpdateBuilder : public SqlBuilder {
public:
    /**
     * @brief Constructor
     */
    UpdateBuilder();

    /**
     * @brief Constructor with table
     * @param table The table to update
     */
    explicit UpdateBuilder(const Table& table);

    /**
     * @brief Specify the table to update
     * @param table The table
     * @return Reference to this builder for method chaining
     */
    UpdateBuilder& table(const Table& table);

    /**
     * @brief Set a column value
     * @param column The column
     * @param value The value to set
     * @return Reference to this builder for method chaining
     */
    UpdateBuilder& set(const Column& column, const Variant& value);

    /**
     * @brief Set multiple column values using variadic templates
     * @tparam Args Types of the column-value pairs (must be convertible to std::pair<Column, Variant>)
     * @param columnValues The column-value pairs to set
     * @return Reference to this builder for method chaining
     */
    template<ColumnValuePair... Args>
    UpdateBuilder& set(Args&&... columnValues) {
        if constexpr (sizeof...(columnValues) > 0) {
            (addColumnValue(std::forward<Args>(columnValues)), ...);
            m_sqlDirty = true;
        }
        return *this;
    }

    /**
     * @brief Set values from a row
     * @param row The row with values to set
     * @return Reference to this builder for method chaining
     */
    UpdateBuilder& setRow(const Row& row);

    /**
     * @brief Add a WHERE condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    UpdateBuilder& where(const SqlCondition& condition);

    /**
     * @brief Add an AND condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    UpdateBuilder& andWhere(const SqlCondition& condition);

    /**
     * @brief Add an OR condition
     * @param condition The condition
     * @return Reference to this builder for method chaining
     */
    UpdateBuilder& orWhere(const SqlCondition& condition);

    /**
     * @brief Build the SQL query string
     * @return The SQL query string
     */
    [[nodiscard]] std::string build() const override;

private:
    // Helper method to add a where clause
    void addWhereClause(const SqlCondition& condition, SqlLogicalOperator logicalOperator);

    // Helper method to add a column-value pair
    void addColumnValue(const std::pair<Column, Variant>& columnValue) {
        m_columnValues[columnValue.first] = columnValue.second;
    }

    // Table information
    std::optional<Table> m_table;

    // Column values
    std::unordered_map<Column, Variant> m_columnValues;

    // WHERE conditions
    struct WhereClause {
        SqlCondition condition;
        SqlLogicalOperator logicalOperator;
        bool isFirst;
    };
    std::vector<WhereClause> m_whereConditions;

    // Helper method to build the SQL query
    void buildSql() const;
};

} // namespace database

#endif // DATABASE_UPDATE_BUILDER_H
