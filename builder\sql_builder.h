﻿#ifndef DATABASE_SQL_BUILDER_H
#define DATABASE_SQL_BUILDER_H

#include <string>
#include <string_view>
#include <unordered_map>
#include <span>

#include "variant.h"
#include "sql_query.h"
#include "sql_table.h"
#include "sql_column.h"
#include "sql_condition.h"

namespace database {

// Concept for types that can be converted to Column
template<typename T>
concept ColumnConvertible = requires(T t) {
    { Column(t) } -> std::convertible_to<Column>;
};

// Concept for types that can be converted to Variant
template<typename T>
concept VariantConvertible = requires(T t) {
    { Variant(t) } -> std::convertible_to<Variant>;
};

// Concept for types that can be used as column-value pairs
template<typename T>
concept ColumnValuePair = requires(T t) {
    { std::pair<Column, Variant>(t) } -> std::convertible_to<std::pair<Column, Variant>>;
};

// Concept for types that can be used as order by clauses
template<typename T>
concept OrderByConvertible = std::is_convertible_v<T, std::pair<Column, SqlSortOrder>>;

/**
 * @brief Base class for SQL query builders
 *
 * This abstract class provides the foundation for building SQL queries
 * using a fluent interface. It defines common methods for all query builders
 * and handles parameter binding to prevent SQL injection.
 */
class SqlBuilder {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~SqlBuilder() = default;

    /**
     * @brief Build the SQL query string
     * @return The SQL query string
     */
    [[nodiscard]] virtual std::string build() const = 0;

    /**
     * @brief Create a SqlQuery object from this builder
     * @param database The database to use for the query
     * @return A SqlQuery object with the SQL and parameters set
     */
    [[nodiscard]] virtual SqlQuery toQuery(SqlDatabase& database) const;

    /**
     * @brief Execute the query directly
     * @param database The database to use for the query
     * @return A SqlQuery object with the results
     */
    [[nodiscard]] virtual SqlQuery execute(SqlDatabase& database) const;

    /**
     * @brief Get the parameters for this query
     * @return The parameter map
     */
    [[nodiscard]] const std::unordered_map<std::string, Variant>& parameters() const noexcept {
        return m_parameters;
    }

    /**
     * @brief Add parameters from a SqlCondition
     * @param condition The condition to get parameters from
     */
    void addConditionParameters(const SqlCondition& condition);

    /**
     * @brief Add parameters from another builder
     * @param builder The builder to get parameters from
     */
    void addBuilderParameters(const SqlBuilder& builder);

protected:
    /**
     * @brief Generate a unique parameter name
     * @param baseName The base name for the parameter
     * @return A unique parameter name
     */
    [[nodiscard]] std::string generateParamName(std::string_view baseName) const;

    /**
     * @brief Add a parameter to the parameter map
     * @param name The parameter name
     * @param value The parameter value
     * @return The parameter placeholder (e.g., ":name")
     */
    [[nodiscard]] std::string addParameter(std::string_view name, const Variant& value);

    /**
     * @brief Add a parameter with an automatically generated name
     * @param value The parameter value
     * @return The parameter placeholder (e.g., ":param1")
     */
    [[nodiscard]] std::string addParameter(const Variant& value);

    /**
     * @brief Join a span of strings with a separator
     * @param strings The strings to join
     * @param separator The separator to use
     * @return The joined string
     */
    [[nodiscard]] static std::string join(std::span<const std::string> strings, std::string_view separator);

    /**
     * @brief Join a span of columns with a separator
     * @param columns The columns to join
     * @param separator The separator to use
     * @return The joined string
     */
    [[nodiscard]] static std::string joinColumns(std::span<const Column> columns, std::string_view separator);

    /**
     * @brief Convert a column to its SQL representation
     * @param column The column to convert
     * @return The SQL string
     */
    [[nodiscard]] static std::string columnToSql(const Column& column);

    /**
     * @brief Convert a table to its SQL representation
     * @param table The table to convert
     * @return The SQL string
     */
    [[nodiscard]] static std::string tableToSql(const Table& table);

    /**
     * @brief Convert a condition to its SQL representation
     * @param condition The condition to convert
     * @return The SQL string
     */
    [[nodiscard]] static std::string conditionToSql(const SqlCondition& condition);

    // Parameter binding map
    mutable std::unordered_map<std::string, Variant> m_parameters;

    // Parameter counter for generating unique names
    mutable int m_paramCounter = 0;

    // Flag to indicate if SQL generation is needed
    mutable bool m_sqlDirty = true;

    // Cached SQL string
    mutable std::string m_cachedSql;
};

} // namespace database

#endif // DATABASE_SQL_BUILDER_H
