# Database Abstraction Classes Refactoring: Dual-Mode Design

## Overview

This document describes the comprehensive refactoring of the database abstraction classes (`SqlTable`, `SqlColumn`, `SqlIndex`, and `SqlRow`) to implement a dual-mode design pattern that addresses the mixed responsibilities issue and provides clear separation of concerns.

## Problem Statement

### Original Issues

1. **Mixed Responsibilities**: Classes mixed metadata access (for existing database objects) with SQL building capabilities
2. **Incomplete Implementation**: The `loadMetadata()` functions were mostly empty or incomplete
3. **Unclear Mode Boundaries**: No clear separation between metadata access vs. SQL building usage
4. **Resource Inefficiency**: Complex caching mechanisms used even for lightweight SQL builders
5. **Poor Delegation**: Classes didn't properly delegate to driver layer for database operations

## Solution: Dual-Mode Design Pattern

### Core Concept

Each abstraction class now operates in one of two distinct modes:

1. **Builder Mode**: Lightweight SQL statement construction without database access
2. **Metadata Mode**: Full database metadata access via driver delegation

### Key Benefits

- **Clear Separation of Concerns**: Each mode has well-defined responsibilities
- **Resource Optimization**: Lightweight objects for SQL building, full caching for metadata access
- **Proper Delegation**: All database operations go through the driver layer
- **Type Safety**: Compile-time and runtime checks prevent incorrect usage
- **Backward Compatibility**: Existing code can be gradually migrated

## Implementation Details

### Mode Enumeration

```cpp
enum class SqlObjectMode {
    Builder,    ///< Lightweight mode for SQL statement building
    Metadata    ///< Full mode with database metadata access via driver
};
```

### SqlTable Class Refactoring

#### Static Factory Methods

```cpp
// Create table in Builder mode
static SqlTable builder(std::string_view name);

// Create table in Metadata mode
static SqlTable metadata(std::string_view name, SqlDriver* driver);
```

#### Mode Management

```cpp
SqlObjectMode mode() const noexcept;
bool isBuilderMode() const noexcept;
bool isMetadataMode() const noexcept;
SqlTable& toBuilderMode();
SqlTable& toMetadataMode(SqlDriver* driver);
```

#### Mode-Aware Operations

- **Builder Mode**: Setters for SQL construction, basic property access
- **Metadata Mode**: Getters that delegate to driver, lazy-loaded metadata

#### Resource Optimization

```cpp
private:
    // Core properties (always present)
    std::string m_name;
    std::string m_schema;
    std::string m_catalog;
    std::string m_alias;
    SqlObjectMode m_mode;
    
    // Driver reference (only used in Metadata mode)
    SqlDriver* m_driver = nullptr;

    // Lazy-loaded data (only used in Metadata mode)
    mutable bool m_metadataLoaded = false;
    mutable std::shared_ptr<SqlTableMetadata> m_metadata;
```

### SqlColumn Class Refactoring

Similar dual-mode design with:
- Static factory methods (`builder()`, `metadata()`)
- Mode management methods
- Mode-aware operations
- Resource optimization

### Driver Delegation

#### Proper Metadata Loading

```cpp
void SqlTable::loadMetadata() const {
    if (!m_driver || m_name.empty() || m_mode != SqlObjectMode::Metadata) {
        return;
    }

    try {
        // Delegate to driver for actual metadata loading
        auto metadata = m_driver->getTableMetadata(*this);
        m_metadata = std::make_shared<SqlTableMetadata>(std::move(metadata));
        m_metadataLoaded = true;
    } catch (const std::exception&) {
        // Handle errors appropriately
        m_metadata = std::make_shared<SqlTableMetadata>();
        m_metadataLoaded = true;
    }
}
```

#### Mode Validation

```cpp
void SqlTable::requireMetadataMode() const {
    if (m_mode != SqlObjectMode::Metadata) {
        throw std::runtime_error("Operation requires Metadata mode");
    }
    if (!m_driver) {
        throw std::runtime_error("Operation requires a valid database driver");
    }
}
```

## Usage Examples

### Builder Mode Usage

```cpp
// Lightweight SQL construction
auto table = SqlTable::builder("users");
table.setSchema("public").setAlias("u");

auto column = SqlColumn::builder("id", &table);
auto condition = column.eq(1);

std::string sql = table.selectSql({"id", "name", "email"});
```

### Metadata Mode Usage

```cpp
// Database metadata access
auto table = SqlTable::metadata("users", driver);

bool exists = table.exists();  // Delegates to driver
auto columns = table.columns();  // Lazy-loaded from database
auto type = table.type();  // Retrieved from database metadata
```

### Mode Switching

```cpp
// Start in Builder mode
auto table = SqlTable::builder("products");

// Switch to Metadata mode when driver becomes available
table.toMetadataMode(driver);

// Switch back to Builder mode for lightweight operations
table.toBuilderMode();
```

## Migration Guide

### For Existing Code

1. **Identify Usage Pattern**: Determine if code is building SQL or accessing metadata
2. **Use Appropriate Factory**: Replace constructors with `builder()` or `metadata()`
3. **Handle Exceptions**: Add error handling for metadata mode operations
4. **Update Driver Usage**: Ensure proper driver delegation

### Example Migration

```cpp
// Before
SqlTable table("users", driver);
auto columns = table.columns();  // May or may not work

// After
SqlTable table = SqlTable::metadata("users", driver);
try {
    auto columns = table.columns();  // Guaranteed to work or throw
} catch (const std::exception& e) {
    // Handle error appropriately
}
```

## Performance Characteristics

### Builder Mode
- **Memory**: Minimal overhead, stack-allocated structures
- **CPU**: Fast object creation and manipulation
- **I/O**: No database access

### Metadata Mode
- **Memory**: Shared pointers and caching for efficiency
- **CPU**: Lazy loading with caching for repeated access
- **I/O**: Controlled database access through driver delegation

## Testing Strategy

### Unit Tests
- Mode switching functionality
- Resource optimization verification
- Error handling for invalid operations
- Driver delegation correctness

### Integration Tests
- End-to-end metadata loading
- SQL generation in both modes
- Performance benchmarks

## Future Enhancements

1. **Column-Level Metadata Loading**: Implement driver delegation for column metadata
2. **Index Support**: Extend dual-mode design to SqlIndex class
3. **Caching Strategies**: Implement configurable caching policies
4. **Performance Monitoring**: Add metrics for mode usage and performance
5. **Additional Validation**: Enhanced compile-time checks for mode correctness

## Conclusion

The dual-mode refactoring successfully addresses the original issues by:

- ✅ **Separating Concerns**: Clear boundaries between SQL building and metadata access
- ✅ **Optimizing Resources**: Mode-appropriate resource usage
- ✅ **Proper Delegation**: All database operations go through driver layer
- ✅ **Maintaining Compatibility**: Gradual migration path for existing code
- ✅ **Improving Reliability**: Better error handling and validation

This design provides a solid foundation for future enhancements while maintaining the flexibility and performance required for both lightweight SQL construction and comprehensive database metadata access.
