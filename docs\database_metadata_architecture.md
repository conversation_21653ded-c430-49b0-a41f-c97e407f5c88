# 数据库元数据架构设计

## 🎯 设计目标

重新设计数据库抽象层，明确各组件的职责分工，消除功能重合，提供类型安全且易用的API。

## 📋 架构概览

### 核心组件分工

#### 1. SQL构建器组件 (builder/)
- **SqlTable**: 表示SQL语句中的表，专注于SQL构建
- **SqlColumn**: 表示SQL语句中的列，专注于SQL构建  
- **SqlIndex**: 表示SQL语句中的索引，专注于SQL构建

**职责**: 
- SQL语句构建和生成
- 查询条件构造
- DDL语句生成

#### 2. 元数据组件 (metadata/)
- **SqlTableMetadata**: 描述数据库表的完整结构信息
- **SqlColumnMetadata**: 描述数据库列的详细属性
- **SqlIndexMetadata**: 描述数据库索引的详细信息
- **SqlConstraintMetadata**: 描述数据库约束信息

**职责**:
- 数据库结构反射
- 元数据存储和查询
- 数据库对象属性描述

#### 3. 驱动接口 (driver/)
- **SqlDriver**: 统一的数据库驱动接口
- **SqliteDriver**: SQLite具体实现

**职责**:
- 数据库连接管理
- 元数据API提供
- SQL执行和事务管理

## 🔄 对象转换关系

### Metadata → Builder
```cpp
// 从元数据创建SQL构建器对象
SqlTable table = tableMetadata.toSqlTable();
SqlIndex index = indexMetadata.toSqlIndex();
SqlColumn column = columnMetadata.toSqlColumn(&table);
```

### Builder → Metadata
```cpp
// 从SQL构建器对象创建元数据
SqlTableMetadata metadata(table);
```

## 📊 API设计原则

### 1. 类型安全
- 使用强类型对象而非字符串
- 编译时类型检查
- 避免运行时类型错误

### 2. 职责单一
- SQL构建器专注于SQL生成
- 元数据类专注于结构描述
- 驱动接口专注于数据库操作

### 3. 一致性
- 统一的命名约定 (Sql前缀)
- 一致的API模式
- 统一的错误处理

### 4. 可扩展性
- 支持多种数据库类型
- 可扩展的约束系统
- 灵活的元数据结构

## 🔧 核心API

### 表操作
```cpp
// 获取所有表
auto tables = driver->getTables();

// 检查表存在性
SqlTable table = driver->table("users");
bool exists = driver->tableExists(table);

// 获取表元数据
SqlTableMetadata metadata = driver->getTableMetadata(table);
```

### 索引操作
```cpp
// 获取表的所有索引
auto indexes = driver->getIndexes(table);

// 检查索引存在性
SqlIndex index = driver->index("idx_name");
bool exists = driver->indexExists(index);

// 获取索引元数据
SqlIndexMetadata metadata = driver->getIndexMetadata(index);
```

### 元数据查询
```cpp
// 按类型过滤
auto views = driver->getTables("", SqlObjectType::View);
auto systemTables = driver->getTables("", SqlObjectType::SystemTable);

// 模式操作
auto schemas = driver->getSchemas();
bool schemaExists = driver->schemaExists("schema_name");
```

## 🏗️ 扩展点

### 1. 新数据库支持
实现SqlDriver接口即可支持新的数据库类型：
```cpp
class PostgreSqlDriver : public SqlDriver {
    // 实现所有虚函数
};
```

### 2. 新约束类型
扩展SqlConstraintType枚举：
```cpp
enum class SqlConstraintType {
    // 现有类型...
    CustomConstraint  // 新增类型
};
```

### 3. 新对象类型
扩展SqlObjectType枚举：
```cpp
enum class SqlObjectType {
    // 现有类型...
    StoredProcedure,  // 新增类型
    Function
};
```

## 🔍 使用示例

### 完整的表分析
```cpp
// 获取表元数据
SqlTable table = driver->table("users");
SqlTableMetadata metadata = driver->getTableMetadata(table);

// 分析列信息
for (const auto& column : metadata.columns()) {
    std::cout << "列: " << column.name() 
              << ", 类型: " << column.nativeTypeName()
              << ", 主键: " << column.isPrimaryKey() << std::endl;
}

// 分析索引信息
for (const auto& index : metadata.indexes()) {
    std::cout << "索引: " << index.name()
              << ", 唯一: " << index.isUnique() << std::endl;
}
```

### SQL生成
```cpp
// 创建索引
SqlIndex index = driver->index("idx_email", "users");
index.addColumn("email").setUnique(true);

std::string createSql = index.createSql(true);  // CREATE UNIQUE INDEX IF NOT EXISTS...
std::string dropSql = index.dropSql(true);     // DROP INDEX IF EXISTS...
```

## 🎯 优势

1. **清晰的职责分工**: 每个组件都有明确的职责
2. **类型安全**: 使用强类型对象，减少错误
3. **易于扩展**: 支持新数据库和新功能
4. **一致的API**: 统一的使用模式
5. **高性能**: 避免不必要的字符串操作
6. **现代C++**: 使用C++20特性和最佳实践

## 🔄 迁移指南

### 旧API → 新API
```cpp
// 旧API (已删除)
auto tableNames = driver->getTableNames();
bool exists = driver->tableExists("users", "");

// 新API
auto tables = driver->getTables();
SqlTable table = driver->table("users");
bool exists = driver->tableExists(table);
```

这种设计提供了更好的类型安全性、可维护性和扩展性，同时保持了API的简洁性和易用性。 