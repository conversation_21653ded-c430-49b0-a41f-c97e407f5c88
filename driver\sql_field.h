#ifndef DATABASE_SQL_FIELD_H
#define DATABASE_SQL_FIELD_H

#include <string>
#include <string_view>

#include "variant.h"

namespace database {

/**
 * @brief Enumeration of database field types
 *
 * Represents the various data types that can be stored in a database field.
 */
enum class FieldType {
    Unknown,    ///< Unknown or unspecified type
    Integer,    ///< Integer type (typically 32-bit)
    BigInt,     ///< Big integer type (typically 64-bit)
    Float,      ///< Single-precision floating-point type
    Double,     ///< Double-precision floating-point type
    Decimal,    ///< Fixed-point decimal type with precision
    Char,       ///< Fixed-length character string
    VarChar,    ///< Variable-length character string
    Text,       ///< Large text data
    Date,       ///< Date value (year, month, day)
    Time,       ///< Time value (hour, minute, second)
    DateTime,   ///< Combined date and time value
    Timestamp,  ///< Timestamp value (typically for versioning)
    Boolean,    ///< Boolean value (true/false)
    Blob,       ///< Binary Large Object
    Binary      ///< Binary data
};

/**
 * @brief Represents a database field/column
 *
 * This class encapsulates a database field with its name, value, and type information.
 * It provides metadata about the field and methods for accessing and manipulating the field value.
 */
class SqlField {
public:
    /**
     * @brief Enumeration for field requirement status
     */
    enum RequiredStatus {
        Unknown = -1,   ///< Requirement status is unknown
        Optional = 0,   ///< Field is optional
        Required = 1    ///< Field is required
    };

    /**
     * @brief Default constructor
     *
     * Creates an empty field with default values.
     */
    SqlField() = default;

    /**
     * @brief Constructor with name and value
     *
     * @param name The name of the field
     * @param value The value of the field
     */
    SqlField(std::string_view name, const Variant& value);

    // Copy semantics
    /**
     * @brief Copy constructor
     *
     * @param other The field to copy
     */
    SqlField(const SqlField& other) = default;

    /**
     * @brief Copy assignment operator
     *
     * @param other The field to copy
     * @return Reference to this field
     */
    SqlField& operator=(const SqlField& other) = default;

    // Move semantics
    /**
     * @brief Move constructor
     *
     * @param other The field to move from
     */
    SqlField(SqlField&& other) noexcept = default;

    /**
     * @brief Move assignment operator
     *
     * @param other The field to move from
     * @return Reference to this field
     */
    SqlField& operator=(SqlField&& other) noexcept = default;

    /**
     * @brief Equality operator
     *
     * Compares field names and values for equality.
     *
     * @param other The field to compare with
     * @return True if fields are equal, false otherwise
     */
    bool operator==(const SqlField& other) const noexcept;

    /**
     * @brief Inequality operator
     *
     * @param other The field to compare with
     * @return True if fields are not equal, false otherwise
     */
    bool operator!=(const SqlField& other) const noexcept;

    /**
     * @brief Get the field name
     *
     * @return The name of the field
     */
    [[nodiscard]] const std::string& name() const noexcept { return m_name; }

    /**
     * @brief Set the field name
     *
     * @param name The new name for the field
     */
    void setName(std::string_view name) { m_name = name; }

    /**
     * @brief Get the field value
     *
     * @return The value of the field
     */
    [[nodiscard]] const Variant& value() const noexcept { return m_value; }

    /**
     * @brief Set the field value
     *
     * @param value The new value for the field
     */
    void setValue(const Variant& value) { m_value = value; }

    /**
     * @brief Get the table name this field belongs to
     *
     * @return The table name
     */
    [[nodiscard]] const std::string& tableName() const noexcept { return m_tableName; }

    /**
     * @brief Set the table name this field belongs to
     *
     * @param tableName The table name
     */
    void setTableName(std::string_view tableName) { m_tableName = tableName; }

    /**
     * @brief Get the default value for this field
     *
     * @return The default value
     */
    [[nodiscard]] Variant defaultValue() const { return m_defaultValue; }

    /**
     * @brief Set the default value for this field
     *
     * @param value The default value
     */
    void setDefaultValue(const Variant& value) { m_defaultValue = value; }

    /**
     * @brief Check if the field value is null
     *
     * @return True if the value is null, false otherwise
     */
    [[nodiscard]] bool isNull() const noexcept { return m_value.isNull(); }

    /**
     * @brief Set whether the field is read-only
     *
     * @param readOnly True to make the field read-only, false otherwise
     */
    void setReadOnly(bool readOnly) noexcept;

    /**
     * @brief Check if the field is read-only
     *
     * @return True if the field is read-only, false otherwise
     */
    [[nodiscard]] bool isReadOnly() const noexcept;

    /**
     * @brief Check if the field is required
     *
     * @return True if the field is required, false otherwise
     */
    [[nodiscard]] constexpr bool isRequired() const noexcept { return m_required; }

    /**
     * @brief Set whether the field is required
     *
     * @param required True to make the field required, false otherwise
     */
    constexpr void setRequired(bool required) noexcept { m_required = required; }

    /**
     * @brief Check if the field is a primary key
     *
     * @return True if the field is a primary key, false otherwise
     */
    [[nodiscard]] constexpr bool isPrimaryKey() const noexcept { return m_primaryKey; }

    /**
     * @brief Set whether the field is a primary key
     *
     * @param primaryKey True to make the field a primary key, false otherwise
     */
    constexpr void setPrimaryKey(bool primaryKey) noexcept { m_primaryKey = primaryKey; }

    /**
     * @brief Check if the field is auto-increment
     *
     * @return True if the field is auto-increment, false otherwise
     */
    [[nodiscard]] constexpr bool isAutoIncrement() const noexcept { return m_autoIncrement; }

    /**
     * @brief Set whether the field is auto-increment
     *
     * @param autoIncrement True to make the field auto-increment, false otherwise
     */
    constexpr void setAutoIncrement(bool autoIncrement) noexcept { m_autoIncrement = autoIncrement; }

    /**
     * @brief Get the field type
     *
     * @return The field type
     */
    [[nodiscard]] constexpr FieldType type() const noexcept { return m_type; }

    /**
     * @brief Set the field type
     *
     * @param type The field type
     */
    constexpr void setType(FieldType type) noexcept { m_type = type; }

private:
    std::string m_name;           ///< The name of the field
    std::string m_tableName;      ///< The name of the table this field belongs to

    Variant m_value;              ///< The current value of the field
    Variant m_defaultValue;       ///< The default value of the field

    FieldType m_type = FieldType::Unknown;  ///< The data type of the field

    bool m_required = false;      ///< Whether the field is required (not nullable)
    bool m_primaryKey = false;    ///< Whether the field is part of the primary key
    bool m_autoIncrement = false; ///< Whether the field is auto-incrementing
    bool m_readOnly = false;      ///< Whether the field is read-only
};

} // namespace database

#endif // DATABASE_SQL_FIELD_H
