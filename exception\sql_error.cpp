﻿#include "sql_error.h"

#include <iostream>

namespace database {

SqlError::SqlError() noexcept
    : m_errorCode(ErrorCode::Unknown),
    m_sqlState(""),
    m_message("") {
}

SqlError::SqlError(
    std::string_view message,
    ErrorCode errorCode,
    std::string_view sqlState) noexcept
    : m_message(message),
    m_errorCode(errorCode),
    m_sqlState(sqlState) {
    std::cerr << "SqlError: " << message << std::endl;
}

SqlError::SqlError(const SqlException& exception) noexcept
    : m_errorCode(exception.errorCode()),
    m_sqlState(exception.sqlState()),
    m_message(exception.message()) {
    std::cerr << "SqlError: " << exception.message() << std::endl;
}

void SqlError::clear() noexcept {
    m_errorCode = ErrorCode::Unknown;
    m_sqlState.clear();
    m_message.clear();
}

SqlException SqlError::toException() const {
    return SqlException(m_message, m_errorCode, m_sqlState);
}

} // namespace database
