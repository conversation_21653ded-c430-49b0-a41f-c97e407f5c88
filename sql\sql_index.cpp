#include "sql_index.h"
#include "sql_table.h"
#include "driver/sql_driver.h"

#include <sstream>
#include <algorithm>
#include <stdexcept>

namespace database {

// Table management
SqlIndex& SqlIndex::setTable(const SqlTable& table) {
    m_table = &table;
    m_tableName = table.name();
    if (!table.schema().empty()) {
        mutableProps().schema = table.schema();
    }
    if (isMetadataMode()) {
        invalidateCache();
    }
    return *this;
}

std::string SqlIndex::tableName() const {
    if (m_table) {
        return m_table->name();
    }
    return m_tableName;
}

SqlIndex& SqlIndex::setTableName(std::string_view tableName) {
    m_tableName = tableName;
    if (isMetadataMode()) {
        invalidateCache();
    }
    return *this;
}

std::string SqlIndex::qualifiedTableName() const {
    if (m_table) {
        return m_table->qualifiedName();
    }

    if (schema().empty()) {
        return m_tableName;
    }
    return std::format("{}.{}", schema(), m_tableName);
}

// Index properties with mode awareness
bool SqlIndex::isUnique() const {
    if (isBuilderMode() && m_uniqueOverride.has_value()) {
        return m_uniqueOverride.value();
    }

    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->unique;
    }
    return false;
}

SqlIndex& SqlIndex::setUnique(bool unique) noexcept {
    m_uniqueOverride = unique;
    return *this;
}

bool SqlIndex::isPrimary() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->primary;
    }
    return false;
}

bool SqlIndex::isClustered() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->clustered;
    }
    return false;
}

const std::string& SqlIndex::indexType() const {
    if (isBuilderMode() && m_indexTypeOverride.has_value()) {
        return m_indexTypeOverride.value();
    }

    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->indexType;
    }

    static const std::string empty;
    return empty;
}

SqlIndex& SqlIndex::setIndexType(std::string_view type) {
    m_indexTypeOverride = type;
    return *this;
}

const std::string& SqlIndex::definition() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->definition;
    }

    static const std::string empty;
    return empty;
}

const std::string& SqlIndex::comment() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->comment;
    }

    static const std::string empty;
    return empty;
}

const std::vector<std::string>& SqlIndex::columns() const {
    if (m_columnsOverride.has_value()) {
        return m_columnsOverride.value();
    }

    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->columns;
    }

    static const std::vector<std::string> empty;
    return empty;
}

SqlIndex& SqlIndex::addColumn(std::string_view columnName) {
    if (!m_columnsOverride.has_value()) {
        m_columnsOverride = std::vector<std::string>();
    }
    m_columnsOverride->emplace_back(columnName);
    return *this;
}

const std::string& SqlIndex::whereClause() const {
    if (m_whereClauseOverride.has_value()) {
        return m_whereClauseOverride.value();
    }

    static const std::string empty;
    return empty;
}

bool SqlIndex::exists() const {
    if (!m_driver) {
        return false;
    }

    return m_driver->indexExists(*this);
}

std::string SqlIndex::createSql(bool ifNotExists) const {
    if (!isValid()) {
        return "";
    }

    std::ostringstream sql;

    sql << "CREATE ";

    if (isUnique()) {
        sql << "UNIQUE ";
    }

    sql << "INDEX ";

    if (ifNotExists) {
        sql << "IF NOT EXISTS ";
    }

    sql << qualifiedName() << " ON " << qualifiedTableName() << " (";

    // Add columns
    const auto& cols = columns();
    for (size_t i = 0; i < cols.size(); ++i) {
        if (i > 0) {
            sql << ", ";
        }
        sql << cols[i];
    }

    sql << ")";

    // Add WHERE clause for partial indexes
    const auto& where = whereClause();
    if (!where.empty()) {
        sql << " WHERE " << where;
    }

    return sql.str();
}

std::string SqlIndex::dropSql(bool ifExists) const {
    if (m_name.empty()) {
        return "";
    }

    std::ostringstream sql;

    sql << "DROP INDEX ";

    if (ifExists) {
        sql << "IF EXISTS ";
    }

    sql << qualifiedName();

    return sql.str();
}

bool SqlIndex::isValid() const {
    return !m_name.empty() && !columns().empty();
}

SqlIndex& SqlIndex::reload() {
    invalidateCache();
    return *this;
}

SqlIndex& SqlIndex::preloadMetadata() {
    ensureMetadataLoaded();
    return *this;
}

const SqlIndex::Metadata* SqlIndex::metadata() const {
    ensureMetadataLoaded();
    return m_metadata.get();
}

void SqlIndex::setMetadata(const Metadata& metadata) {
    m_metadata = std::make_unique<Metadata>(metadata);
    m_metadataLoaded = true;
}

void SqlIndex::loadMetadata() const {
    if (!driver() || name().empty() || !isMetadataMode()) {
        return;
    }

    try {
        // Delegate to driver for actual metadata loading
        auto metadata = driver()->getIndexMetadata(*this);
        m_metadata = std::make_shared<SqlIndexMetadata>(std::move(metadata));
        m_metadataLoaded = true;
    } catch (const std::exception&) {
        // If loading fails, create empty metadata and mark as loaded
        m_metadata = std::make_shared<SqlIndexMetadata>();
        m_metadata->tableName = m_tableName;
        m_metadataLoaded = true;
    }
}

void SqlIndex::invalidateCache() {
    // Only clear cache if in metadata mode
    if (isMetadataMode()) {
        m_metadataLoaded = false;
        m_metadata.reset();
    }
}

void SqlIndex::ensureMetadataLoaded() const {
    if (isMetadataMode() && !m_metadataLoaded) {
        loadMetadata();
    }
}

} // namespace database