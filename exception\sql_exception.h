#ifndef DATABASE_DATABASE_EXCEPTION_H
#define DATABASE_DATABASE_EXCEPTION_H

#include <stdexcept>
#include <string>
#include <string_view>

#include "sql_enums.h"

namespace database {

/**
 * @brief Base exception class for database errors
 */
class SqlException : public std::runtime_error {
public:
    /**
     * @brief Constructor
     * @param message The error message
     * @param errorCode The error code
     * @param sqlState The SQL state
     */
    SqlException(
        std::string_view message,
        ErrorCode errorCode = ErrorCode::Unknown,
        std::string_view sqlState = "") noexcept;

    /**
     * @brief Get the error code
     * @return The error code
     */
    [[nodiscard]] ErrorCode errorCode() const noexcept { return m_errorCode; }

    /**
     * @brief Get the SQL state
     * @return The SQL state
     */
    [[nodiscard]] std::string_view sqlState() const noexcept { return m_sqlState; }

    /**
     * @brief Get the error message
     * @return The error message
     */
    [[nodiscard]] std::string_view message() const noexcept { return what(); }

private:
    ErrorCode m_errorCode;
    std::string m_sqlState;
};

} // namespace database

#endif // DATABASE_DATABASE_EXCEPTION_H
