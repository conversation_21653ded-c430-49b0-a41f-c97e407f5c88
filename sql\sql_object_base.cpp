#include "sql_object_base.h"

#include <stdexcept>
#include <format>

namespace database {

SqlObjectBase& SqlObjectBase::setName(std::string_view name) {
    m_props.name = name;
    if (isMetadataMode()) {
        invalidateCache();  // Clear cached metadata when name changes
    }
    return *this;
}

SqlObjectBase& SqlObjectBase::setSchema(std::string_view schema) {
    m_props.schema = schema;
    if (isMetadataMode()) {
        invalidateCache();  // Clear cached metadata when schema changes
    }
    return *this;
}

SqlObjectBase& SqlObjectBase::setAlias(std::string_view alias) {
    m_props.alias = alias;
    return *this;
}

SqlObjectBase& SqlObjectBase::setDriver(SqlDriver* driver) {
    if (driver) {
        switchToMetadataMode(driver);
    } else {
        switchToBuilderMode();
    }
    return *this;
}

void SqlObjectBase::switchToBuilderMode() {
    m_mode = SqlObjectMode::Builder;
    m_driver = nullptr;  // Remove driver reference for optimization
    invalidateCache();   // Clear any cached metadata
}

void SqlObjectBase::switchToMetadataMode(SqlDriver* driver) {
    m_mode = SqlObjectMode::Metadata;
    m_driver = driver;
}

std::string SqlObjectBase::qualifiedName() const {
    if (m_props.name.empty()) {
        return {};
    }

    // If we have an alias, use it as the qualified name
    if (hasAlias()) {
        return m_props.alias;
    }

    // If we have a schema, use schema.name format
    if (hasSchema()) {
        return std::format("{}.{}", m_props.schema, m_props.name);
    }

    // Otherwise just use the name
    return m_props.name;
}

std::string SqlObjectBase::toSql() const {
    if (hasAlias() && !hasSchema()) {
        // Only show alias if we don't have a schema (to avoid schema.name AS alias)
        return std::format("{} AS {}", m_props.name, m_props.alias);
    }
    return qualifiedName();
}

void SqlObjectBase::requireMetadataMode() const {
    if (!m_driver) {
        throw std::runtime_error(
            std::format("Operation requires database metadata access. "
                       "Please set a database driver using setDriver() before accessing metadata properties."));
    }
    // Note: Mode switching is now automatic when driver is set, so we don't need to check mode explicitly
}

} // namespace database
