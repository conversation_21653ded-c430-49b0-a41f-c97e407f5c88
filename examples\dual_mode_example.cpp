/**
 * @file dual_mode_example.cpp
 * @brief Comprehensive example demonstrating the optimized dual-mode design
 *
 * This example shows the complete refactored database abstraction classes:
 * - SqlTable, SqlColumn, SqlIndex with dual-mode operation
 * - Memory optimization and variable consolidation
 * - C++20 features and modern best practices
 * - Performance comparison between modes
 */

#include <iostream>
#include <memory>
#include <chrono>
#include <vector>
#include <ranges>

// Include the optimized refactored classes
#include "sql/sql_table.h"
#include "sql/sql_column.h"
#include "sql/sql_index.h"
#include "sql/sql_concepts.h"
#include "driver/sql_driver.h"

using namespace database;
using namespace std::chrono;

// C++20 concept demonstration with simplified API
template<SqlDatabaseObject T>
void demonstrateConceptUsage(const T& obj) {
    std::cout << "Object name: " << obj.name() << std::endl;
    std::cout << "Qualified name: " << obj.qualifiedName() << std::endl;
    std::cout << "Has schema: " << (obj.hasSchema() ? "Yes" : "No") << std::endl;
}

void demonstrateSimplifiedAPI() {
    std::cout << "\n=== Simplified API Demonstration ===\n";

    // Create table with simple constructor (no mode methods needed)
    auto table = SqlTable("users");
    table.setSchema("public").setCatalog("mydb").setAlias("u");

    std::cout << "Table properties:\n";
    demonstrateConceptUsage(table);
    std::cout << "Catalog: " << table.catalog() << std::endl;

    // Create columns with fluent interface
    auto idColumn = SqlColumn("id", &table);
    auto nameColumn = SqlColumn("name", &table);
    auto emailColumn = SqlColumn("email", &table);

    std::cout << "\nColumn properties:\n";
    demonstrateConceptUsage(idColumn);

    // Create index with optimized structure
    auto index = SqlIndex("idx_users_email");
    index.setTable(&table)
         .setUnique(true)
         .setIndexType("BTREE");

    std::cout << "\nIndex properties:\n";
    demonstrateConceptUsage(index);
    std::cout << "Table name: " << index.tableName() << std::endl;
    std::cout << "Is unique: " << (index.isUnique() ? "Yes" : "No") << std::endl;

    // Demonstrate SqlRow with C++20 improvements
    SqlRow row{
        {"id", Variant(1)},
        {"name", Variant("John Doe")},
        {"email", Variant("<EMAIL>")}
    };

    std::cout << "\nRow data:\n";
    std::cout << "ID: " << row.value("id").toString() << std::endl;
    std::cout << "Name: " << row.value("name").toString() << std::endl;
    std::cout << "Email: " << row.value("email").toString() << std::endl;
}

void demonstrateAutomaticModeSwitching() {
    std::cout << "\n=== Automatic Mode Switching Demonstration ===\n";

    // Create table without driver (automatically uses lightweight mode)
    auto table = SqlTable("users");
    table.setSchema("public");

    std::cout << "Table created without driver - optimized for SQL building\n";
    std::cout << "Qualified name: " << table.qualifiedName() << std::endl;

    // Note: In a real scenario, you would have an actual SqlDriver implementation
    SqlDriver* driver = nullptr;  // In real code: driver = new SqliteDriver();

    if (driver) {
        // Setting driver automatically enables metadata access
        table.setDriver(driver);
        std::cout << "Driver set - metadata operations now available\n";

        // These operations would delegate to the driver for actual database queries
        try {
            bool exists = table.exists();  // Would query database
            auto columns = table.columns();  // Would load from database
            auto type = table.type();  // Would get from database metadata

            std::cout << "Metadata operations executed successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Metadata operation: " << e.what() << std::endl;
        }

        // Create column with driver (automatically enables metadata access)
        auto column = SqlColumn("id", &table, driver);

        try {
            auto type = column.type();  // Would load from database
            bool isPK = column.isPrimaryKey();  // Would check database metadata
            std::cout << "Column metadata operations executed successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "Column metadata operation: " << e.what() << std::endl;
        }
    } else {
        std::cout << "No driver available - using lightweight SQL building mode" << std::endl;

        // Without driver, operations focus on SQL generation
        auto column = table.column("id");
        std::cout << "Column reference created: " << column.qualifiedName() << std::endl;

        // SQL building operations work without driver
        std::cout << "SQL building operations available without driver" << std::endl;
    }
}

void demonstrateModeSwitching() {
    std::cout << "\n=== Mode Switching Demonstration ===\n";

    // Start with Builder mode
    auto table = SqlTable::builder("products");
    std::cout << "Initial mode: " << (table.isBuilderMode() ? "Builder" : "Metadata") << std::endl;

    // Switch to Metadata mode (would require a valid driver)
    SqlDriver* driver = nullptr;  // In real code: driver = new SqliteDriver();
    if (driver) {
        table.toMetadataMode(driver);
        std::cout << "After switching: " << (table.isMetadataMode() ? "Metadata" : "Builder") << std::endl;

        // Switch back to Builder mode
        table.toBuilderMode();
        std::cout << "After switching back: " << (table.isBuilderMode() ? "Builder" : "Metadata") << std::endl;
    } else {
        std::cout << "Mode switching requires a valid driver for Metadata mode" << std::endl;
    }
}

void demonstrateMemoryOptimization() {
    std::cout << "\n=== Memory Optimization Demonstration ===\n";

    // Demonstrate memory efficiency with C++20 ranges
    std::vector<SqlTable> builderTables;
    std::vector<SqlTable> metadataTables;

    // Create multiple builder mode tables (lightweight)
    for (int i = 0; i < 1000; ++i) {
        builderTables.emplace_back(SqlTable::builder(std::format("table_{}", i)));
    }

    std::cout << "Created 1000 builder mode tables (minimal memory footprint)" << std::endl;
    std::cout << "Each table stores only: name, schema, alias, mode (no caching overhead)" << std::endl;

    // Demonstrate C++20 ranges usage
    auto tablesWithSchema = builderTables
        | std::views::filter([](const auto& table) { return table.hasSchema(); })
        | std::views::take(10);

    std::cout << "Using C++20 ranges to filter tables with schema (first 10):" << std::endl;
    for (const auto& table : tablesWithSchema) {
        std::cout << "  - " << table.qualifiedName() << std::endl;
    }
}

void demonstratePerformanceComparison() {
    std::cout << "\n=== Performance Comparison ===\n";

    const int iterations = 10000;

    // Builder mode performance
    auto start = high_resolution_clock::now();
    for (int i = 0; i < iterations; ++i) {
        auto table = SqlTable::builder(std::format("perf_table_{}", i));
        table.setSchema("test_schema").setAlias(std::format("t{}", i));
        auto column = SqlColumn::builder("id", &table);
        auto index = SqlIndex::builder(std::format("idx_{}", i));
        index.setTable(table).setUnique(true);

        // Simulate SQL generation
        [[maybe_unused]] auto sql = table.qualifiedName();
        [[maybe_unused]] auto colSql = column.qualifiedName();
        [[maybe_unused]] auto idxSql = index.qualifiedName();
    }
    auto end = high_resolution_clock::now();
    auto builderDuration = duration_cast<microseconds>(end - start);

    std::cout << "Builder mode (" << iterations << " iterations): "
              << builderDuration.count() << " microseconds" << std::endl;
    std::cout << "Average per operation: "
              << static_cast<double>(builderDuration.count()) / iterations << " microseconds" << std::endl;

    std::cout << "\nBuilder mode benefits:" << std::endl;
    std::cout << "  ✓ Zero database I/O" << std::endl;
    std::cout << "  ✓ Minimal memory allocation" << std::endl;
    std::cout << "  ✓ Fast object creation and manipulation" << std::endl;
    std::cout << "  ✓ Optimized for SQL generation" << std::endl;
}

int main() {
    std::cout << "Database Abstraction Classes - Simplified Dual Mode API\n";
    std::cout << "=======================================================\n";

    try {
        demonstrateSimplifiedAPI();
        demonstrateAutomaticModeSwitching();
        demonstrateMemoryOptimization();
        demonstratePerformanceComparison();

        std::cout << "\n=== API Simplification Summary ===\n";
        std::cout << "✓ Hidden Mode Complexity: Mode switching is automatic and transparent\n";
        std::cout << "✓ Unified API: Single interface for both SQL building and metadata access\n";
        std::cout << "✓ Driver-Based Switching: Setting driver automatically enables metadata operations\n";
        std::cout << "✓ Backward Compatibility: Existing code patterns still work\n";
        std::cout << "✓ Performance Maintained: All optimizations preserved internally\n";
        std::cout << "✓ SqlRow Optimized: Enhanced with C++20 features while remaining lightweight\n";
        std::cout << "✓ Concept Updates: Simplified concepts reflect the cleaner API\n";
        std::cout << "✓ Error Messages: Clear guidance without exposing mode complexity\n";

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
