#include "sql_record.h"
#include <stdexcept>
#include <algorithm>

namespace database {

SqlRecord::SqlRecord(const std::vector<SqlField>& fields) {
    for (const auto& field : fields) {
        addField(field);
    }
}

SqlRecord::SqlRecord(const std::unordered_map<std::string, Variant>& map) {
    for (const auto& [key, value] : map) {
        addField({key, value});
    }
}

const SqlField& SqlRecord::field(size_t index) const {
    if (index >= m_fields.size()) {
        throw std::out_of_range("Field index out of range");
    }

    return m_fields[index];
}

const SqlField& SqlRecord::field(std::string_view name) const {
    auto it = m_fieldMap.find(std::string(name));
    if (it == m_fieldMap.end()) {
        throw std::out_of_range("Field not found: " + std::string(name));
    }

    return m_fields[it->second];
}

bool SqlRecord::hasField(std::string_view name) const {
    return m_fieldMap.find(std::string(name)) != m_fieldMap.end();
}

const Variant& SqlRecord::value(size_t index) const {
    return field(index).value();
}

const Variant& SqlRecord::value(std::string_view name) const {
    return field(name).value();
}

bool SqlRecord::isNull(size_t index) const {
    return field(index).isNull();
}

bool SqlRecord::isNull(std::string_view name) const {
    return field(name).isNull();
}

void SqlRecord::addField(const SqlField& field) {
    const std::string& name = field.name();

    // Check if the field already exists
    auto it = m_fieldMap.find(name);
    if (it != m_fieldMap.end()) {
        // Update the existing field
        m_fields[it->second] = field;
    } else {
        // Add a new field
        m_fieldMap[name] = m_fields.size();
        m_fields.push_back(field);
    }
}

void SqlRecord::setValue(std::string_view name, const Variant& value) {
    auto it = m_fieldMap.find(std::string(name));
    if (it == m_fieldMap.end()) {
        // Add a new field
        addField({std::string(name), value});
    } else {
        // Update the existing field
        m_fields[it->second].setValue(value);
    }
}

void SqlRecord::setValue(size_t index, const Variant& value) {
    if (index >= m_fields.size()) {
        throw std::out_of_range("Field index out of range");
    }

    m_fields[index].setValue(value);
}

std::unordered_map<std::string, Variant> SqlRecord::toMap() const {
    std::unordered_map<std::string, Variant> map;
    map.reserve(m_fields.size());

    for (const auto& field : m_fields) {
        map[field.name()] = field.value();
    }

    return map;
}

std::vector<std::string> SqlRecord::fieldNames() const {
    std::vector<std::string> names;
    names.reserve(m_fields.size());

    // Use transform algorithm to extract field names
    std::transform(m_fields.begin(), m_fields.end(),
                   std::back_inserter(names),
                   [](const auto& field) { return field.name(); });

    return names;
}

void SqlRecord::clear() noexcept {
    m_fields.clear();
    m_fieldMap.clear();
}

bool SqlRecord::operator==(const SqlRecord& other) const noexcept {
    if (m_fields.size() != other.m_fields.size()) {
        return false;
    }

    // Compare each field
    for (size_t i = 0; i < m_fields.size(); ++i) {
        if (m_fields[i] != other.m_fields[i]) {
            return false;
        }
    }

    return true;
}

bool SqlRecord::operator!=(const SqlRecord& other) const noexcept {
    return !(*this == other);
}

void SqlRecord::replaceField(int pos, const SqlField& field) {
    if (pos < 0 || pos >= static_cast<int>(m_fields.size())) {
        throw std::out_of_range("Field position out of range");
    }

    // Remove old field from map
    const std::string& oldName = m_fields[pos].name();
    m_fieldMap.erase(oldName);

    // Add new field to map
    const std::string& newName = field.name();
    m_fieldMap[newName] = static_cast<size_t>(pos);

    // Replace field in vector
    m_fields[pos] = field;
}

void SqlRecord::insertField(int pos, const SqlField& field) {
    if (pos < 0 || pos > static_cast<int>(m_fields.size())) {
        throw std::out_of_range("Field position out of range");
    }

    // Insert field into vector
    m_fields.insert(m_fields.begin() + pos, field);

    // Update indices in map for fields after the insertion point
    for (size_t i = m_fields.size() - 1; i > static_cast<size_t>(pos); --i) {
        m_fieldMap[m_fields[i].name()] = i;
    }

    // Add new field to map
    m_fieldMap[field.name()] = static_cast<size_t>(pos);
}

void SqlRecord::removeField(int pos) {
    if (pos < 0 || pos >= static_cast<int>(m_fields.size())) {
        throw std::out_of_range("Field position out of range");
    }

    // Remove from map
    const std::string& name = m_fields[pos].name();
    m_fieldMap.erase(name);

    // Remove from vector
    m_fields.erase(m_fields.begin() + pos);

    // Update indices in map for fields after the removal point
    for (size_t i = static_cast<size_t>(pos); i < m_fields.size(); ++i) {
        m_fieldMap[m_fields[i].name()] = i;
    }
}

void SqlRecord::clearValues() {
    for (auto& field : m_fields) {
        field.setValue(Variant());  // Set to null/empty
    }
}

} // namespace database
