#include "connection_params.h"
#include <sstream>
#include <stdexcept>
#include <regex>
#include <algorithm>

namespace database {

std::string ConnectionParams::toString() const {
    std::ostringstream oss;
    
    // Format: driver://username:password@host:port/database?option1=value1&option2=value2
    if (!m_driverName.empty()) {
        oss << m_driverName << "://";
    }
    
    if (!m_userName.empty()) {
        oss << m_userName;
        if (!m_password.empty()) {
            oss << ":" << m_password;
        }
        oss << "@";
    }
    
    if (!m_host.empty()) {
        oss << m_host;
        if (m_port > 0) {
            oss << ":" << m_port;
        }
    }
    
    if (!m_database.empty()) {
        oss << "/" << m_database;
    }
    
    // Add options
    if (!m_options.empty()) {
        oss << "?";
        bool first = true;
        for (const auto& [key, value] : m_options) {
            if (!first) {
                oss << "&";
            }
            oss << key << "=" << value;
            first = false;
        }
    }
    
    return oss.str();
}

ConnectionParams ConnectionParams::fromString(std::string_view connectionString) {
    ConnectionParams params;
    
    if (connectionString.empty()) {
        return params;
    }
    
    std::string str(connectionString);
    
    // Parse driver
    auto driverPos = str.find("://");
    if (driverPos != std::string::npos) {
        params.setDriverName(str.substr(0, driverPos));
        str = str.substr(driverPos + 3);
    }
    
    // Parse options (query string)
    auto queryPos = str.find('?');
    std::string queryString;
    if (queryPos != std::string::npos) {
        queryString = str.substr(queryPos + 1);
        str = str.substr(0, queryPos);
    }
    
    // Parse database
    auto dbPos = str.find('/');
    if (dbPos != std::string::npos) {
        std::string database = str.substr(dbPos + 1);
        if (!database.empty()) {
            params.setDatabase(database);
        }
        str = str.substr(0, dbPos);
    }
    
    // Parse authentication and host
    auto atPos = str.find('@');
    if (atPos != std::string::npos) {
        // Parse username:password
        std::string auth = str.substr(0, atPos);
        auto colonPos = auth.find(':');
        if (colonPos != std::string::npos) {
            params.setUserName(auth.substr(0, colonPos));
            params.setPassword(auth.substr(colonPos + 1));
        } else {
            params.setUserName(auth);
        }
        str = str.substr(atPos + 1);
    }
    
    // Parse host:port
    if (!str.empty()) {
        auto colonPos = str.find(':');
        if (colonPos != std::string::npos) {
            params.setHost(str.substr(0, colonPos));
            try {
                int port = std::stoi(str.substr(colonPos + 1));
                params.setPort(port);
            } catch (const std::exception&) {
                throw std::invalid_argument("Invalid port number in connection string");
            }
        } else {
            params.setHost(str);
        }
    }
    
    // Parse query string options
    if (!queryString.empty()) {
        std::istringstream queryStream(queryString);
        std::string option;
        while (std::getline(queryStream, option, '&')) {
            auto eqPos = option.find('=');
            if (eqPos != std::string::npos) {
                std::string key = option.substr(0, eqPos);
                std::string value = option.substr(eqPos + 1);
                params.setOption(key, value);
            }
        }
    }
    
    return params;
}

} // namespace database 