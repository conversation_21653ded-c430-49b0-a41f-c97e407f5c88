﻿#ifndef DATABASE_SQL_DRIVER_PLUGIN_H
#define DATABASE_SQL_DRIVER_PLUGIN_H

#include <memory>
#include <string_view>

#include "sql_driver.h"

namespace database {

/**
 * @brief Interface for SQL driver plugins
 *
 * This class defines the interface for SQL driver plugins.
 * Each plugin must implement this interface to provide a driver factory.
 */
class SqlDriverPlugin {
public:
    virtual ~SqlDriverPlugin() = default;

    /**
     * @brief Create a new driver instance
     * @return A unique pointer to the driver
     */
    [[nodiscard]] virtual std::unique_ptr<SqlDriver> createDriver() = 0;

    /**
     * @brief Get the driver name
     * @return The driver name
     */
    [[nodiscard]] virtual std::string_view driverName() const = 0;

    /**
     * @brief Get the plugin version
     * @return The plugin version
     */
    [[nodiscard]] virtual std::string_view pluginVersion() const = 0;

    /**
     * @brief Get the plugin description
     * @return The plugin description
     */
    [[nodiscard]] virtual std::string_view pluginDescription() const = 0;
};

// Define a macro for plugin export
#ifdef _WIN32
#define SQL_DRIVER_PLUGIN_EXPORT extern "C" __declspec(dllexport)
#else
#define SQL_DRIVER_PLUGIN_EXPORT extern "C"
#endif

// Define a macro for plugin implementation
#define SQL_DRIVER_PLUGIN_IMPL(PluginClass) \
    SQL_DRIVER_PLUGIN_EXPORT database::SqlDriverPlugin* createDriverPlugin() { \
        return new PluginClass(); \
    }

} // namespace database

#endif // DATABASE_SQL_DRIVER_PLUGIN_H
