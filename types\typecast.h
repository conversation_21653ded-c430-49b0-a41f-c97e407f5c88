﻿#ifndef TYPECAST_H
#define TYPECAST_H

#include <iostream>
#include <functional>

#include "metatype.h"
#include "id.h"

template<typename To, typename From>
static void cast_warning(const char* description, const From& from, const To& to) {
    return;
    std::cerr << description
              << "(" << typeid(From).name() << " to " << typeid(To).name() << ") "
              << std::to_string(from) << " to " << std::to_string(to)
              << std::endl;
}

template<typename To, typename From>
To numeric_cast(const From& value);

template<typename To, typename From>
To string_cast(const From& value);

template <typename To, typename From>
To pair_cast(const From& value);

template<typename To, typename From>
View view_cast(const From& value);

template<typename To, typename From>
To basic_cast(const From& value) {
    // static_assert((std::is_arithmetic_v<To>
    //                || is_string_v<To>
    //                || std::is_same_v<To, View>
    //                || std::is_same_v<To, Variant>),
    //               "basic_cast: Target and Source type must be basic type.");

    if constexpr (std::is_arithmetic_v<To>)
        return numeric_cast<To>(value);
    if constexpr (is_string_v<To>)
        return string_cast<To>(value);
    if constexpr (std::is_same_v<To, View>)
        return view_cast<To>(value);
    if constexpr (std::is_same_v<To, Variant>)
        return Variant(value);
    return To();
}

template<typename To, typename From>
To numeric_cast(const From& value) {
    static_assert(std::is_arithmetic_v<To>, "numeric_cast: Target type must be numeric.");
    if constexpr (std::is_same_v<From, To>) return value;
    
    if constexpr (std::is_integral_v<From> && std::is_integral_v<To>) {
        // integer to integer
        const auto retValue = static_cast<To>(value);
        constexpr auto to_max = (std::numeric_limits<To>::max)();
        constexpr auto to_min = (std::numeric_limits<To>::min)();

        if (static_cast<long double>(value) > static_cast<long double>(to_max) ||
            static_cast<long double>(value) < static_cast<long double>(to_min)) {
            cast_warning("numeric_cast[Overflow]", value, retValue);
        }
        return retValue;
    }
    if constexpr (std::is_integral_v<From> && std::is_floating_point_v<To>) {
        // 整数到浮点数的转换
        const auto retValue = static_cast<To>(value);
        if (value != static_cast<From>(retValue)) {
            cast_warning("numeric_cast[Overflow]", value, retValue);
        }
        return retValue;
    }
    if constexpr (std::is_floating_point_v<From> && std::is_integral_v<To>) {
        // 浮点数到整数的转换
        const auto retValue = static_cast<To>(value);
        constexpr auto to_max = (std::numeric_limits<To>::max)();
        constexpr auto to_min = (std::numeric_limits<To>::min)();
        if (value != value) { // 检查NaN
            cast_warning("numeric_cast[NaN]", value, retValue);
        } else if (value > static_cast<From>(to_max)
                   || value < static_cast<From>(to_min)) {
            cast_warning("numeric_cast[Overflow]", value, retValue);
        }
        return retValue;
    }
    if constexpr (std::is_floating_point_v<From> && std::is_floating_point_v<To>) {
        // 浮点数到浮点数的转换
        const auto retValue = static_cast<To>(value);
        if (value != value) { // 检查NaN
            cast_warning("numeric_cast[NaN]", value, retValue);
        } else if (value > (std::numeric_limits<To>::max)()
                   || value < -(std::numeric_limits<To>::max)()) {
            cast_warning("numeric_cast[Overflow]", value, retValue);
        } else if (std::abs(value - static_cast<From>(retValue)) > std::numeric_limits<From>::epsilon()) {
            cast_warning("numeric_cast[Unequal]", value, retValue);
        }
        return retValue;
    }
    if constexpr (is_string_v<From>) {
        if constexpr ( std::is_integral_v<To>) {
            if constexpr (std::is_signed_v<To>)
                return static_cast<To>(std::stoll(string_cast<std::string>(value)));
            else
                return static_cast<To>(std::stoull(string_cast<std::string>(value)));
        }
        if constexpr (std::is_floating_point_v<To>) {
            return static_cast<To>(std::stod(string_cast<std::string>(value)));
        }
    }
    if constexpr (is_container_v<From>) {
        return value.size();
    }
    if constexpr (is_pair_v<From>) {
        return numeric_cast<To>(value.first);
    }
    if constexpr (std::is_same_v<From, Variant>) {
        return value.template to<To>();
    }
    return 0;
}

// UTF-8 to UTF-32
static std::u32string utf8_to_utf32(const std::string& str) {
    std::u32string result;
    for (size_t i = 0; i < str.length();) {
        char32_t codepoint = 0;
        unsigned char c = static_cast<unsigned char>(str[i]);

        if (c <= 0x7F) {  // 1 byte
            codepoint = c;
            i += 1;
        } else if ((c & 0xE0) == 0xC0) {  // 2 bytes
            if (i + 1 >= str.length()) break;
            codepoint = ((str[i] & 0x1F) << 6) | (str[i + 1] & 0x3F);
            i += 2;
        } else if ((c & 0xF0) == 0xE0) {  // 3 bytes
            if (i + 2 >= str.length()) break;
            codepoint = ((str[i] & 0x0F) << 12) |
                        ((str[i + 1] & 0x3F) << 6) |
                        (str[i + 2] & 0x3F);
            i += 3;
        } else if ((c & 0xF8) == 0xF0) {  // 4 bytes
            if (i + 3 >= str.length()) break;
            codepoint = ((str[i] & 0x07) << 18) |
                        ((str[i + 1] & 0x3F) << 12) |
                        ((str[i + 2] & 0x3F) << 6) |
                        (str[i + 3] & 0x3F);
            i += 4;
        } else {
            i += 1;  // Skip invalid byte
            continue;
        }
        result.push_back(codepoint);
    }
    return result;
}

// UTF-8 to UTF-32
static std::u32string utf8_to_utf32(const char* str, size_t len) {
    std::u32string result;
    for (size_t i = 0; i < len;) {
        char32_t codepoint = 0;
        unsigned char c = static_cast<unsigned char>(str[i]);

        if (c <= 0x7F) {  // 1 byte
            codepoint = c;
            i += 1;
        } else if ((c & 0xE0) == 0xC0) {  // 2 bytes
            if (i + 1 >= len) break;
            codepoint = ((str[i] & 0x1F) << 6) | (str[i + 1] & 0x3F);
            i += 2;
        } else if ((c & 0xF0) == 0xE0) {  // 3 bytes
            if (i + 2 >= len) break;
            codepoint = ((str[i] & 0x0F) << 12) |
                        ((str[i + 1] & 0x3F) << 6) |
                        (str[i + 2] & 0x3F);
            i += 3;
        } else if ((c & 0xF8) == 0xF0) {  // 4 bytes
            if (i + 3 >= len) break;
            codepoint = ((str[i] & 0x07) << 18) |
                        ((str[i + 1] & 0x3F) << 12) |
                        ((str[i + 2] & 0x3F) << 6) |
                        (str[i + 3] & 0x3F);
            i += 4;
        } else {
            i += 1;  // Skip invalid byte
            continue;
        }
        result.push_back(codepoint);
    }
    return result;
}

// UTF-32 to UTF-8
static std::string utf32_to_utf8(const std::u32string& str) {
    std::string result;
    for (char32_t c : str) {
        if (c <= 0x7F) {
            result.push_back(static_cast<char>(c));
        } else if (c <= 0x7FF) {
            result.push_back(static_cast<char>(0xC0 | (c >> 6)));
            result.push_back(static_cast<char>(0x80 | (c & 0x3F)));
        } else if (c <= 0xFFFF) {
            result.push_back(static_cast<char>(0xE0 | (c >> 12)));
            result.push_back(static_cast<char>(0x80 | ((c >> 6) & 0x3F)));
            result.push_back(static_cast<char>(0x80 | (c & 0x3F)));
        } else if (c <= 0x10FFFF) {
            result.push_back(static_cast<char>(0xF0 | (c >> 18)));
            result.push_back(static_cast<char>(0x80 | ((c >> 12) & 0x3F)));
            result.push_back(static_cast<char>(0x80 | ((c >> 6) & 0x3F)));
            result.push_back(static_cast<char>(0x80 | (c & 0x3F)));
        }
    }
    return result;
}

// UTF-32 to UTF-16
static std::u16string utf32_to_utf16(const std::u32string& str) {
    std::u16string result;
    for (char32_t c : str) {
        if (c <= 0xFFFF) {
            result.push_back(static_cast<char16_t>(c));
        } else if (c <= 0x10FFFF) {
            char32_t adjusted = c - 0x10000;
            result.push_back(static_cast<char16_t>(0xD800 + (adjusted >> 10)));
            result.push_back(static_cast<char16_t>(0xDC00 + (adjusted & 0x3FF)));
        }
    }
    return result;
}

// UTF-16 to UTF-32
static std::u32string utf16_to_utf32(const char16_t* str, size_t len) {
    std::u32string result;
    for (size_t i = 0; i < len;) {
        char32_t codepoint;
        char16_t c = str[i++];

        if (c >= 0xD800 && c <= 0xDBFF && i < len) {
            char16_t c2 = str[i];
            if (c2 >= 0xDC00 && c2 <= 0xDFFF) {
                codepoint = 0x10000;
                codepoint += (c - 0xD800) << 10;
                codepoint += (c2 - 0xDC00);
                i++;
            } else {
                codepoint = c;
            }
        } else {
            codepoint = c;
        }
        result.push_back(codepoint);
    }
    return result;
}

template<typename To, typename From>
static To string_convert(const From& str) {
    if constexpr (std::is_same_v<From, To>) return str;

    if constexpr (std::is_array_v<From>) {
        const size_t n = sizeof(str)/sizeof(str[0]);
        if constexpr (std::is_same_v<From, char[n]>) {
            // To result;
            // for (size_t i = 0; i < n; ++i) {
            //     //if (i == n-1 && str[i] == '\0') break;  // 以null字符结尾
            //     result += str[i];
            // }
        }
        return To();
    }
    if constexpr (std::is_same_v<From, const char*> && std::is_same_v<To, std::string>) {
        return std::string(str);
    }
    //乱码 if constexpr (std::is_same_v<From, std::string> && std::is_same_v<To, const char*>)
    //     return str.data();

    // 转换为UTF-32作为中间表示
    std::u32string u32str;
    if constexpr (std::is_same_v<From, const char*> || std::is_same_v<From, char*>) {
        u32str = utf8_to_utf32(str, strlen(str));
    } else if constexpr (std::is_same_v<From, const signed char*> || std::is_same_v<From, signed char*>) {
        u32str = utf8_to_utf32(reinterpret_cast<const char*>(str), strlen(reinterpret_cast<const char*>(str)));
    } else if constexpr (std::is_same_v<From, const unsigned char*> || std::is_same_v<From, unsigned char*>) {
        u32str = utf8_to_utf32(reinterpret_cast<const char*>(str), strlen(reinterpret_cast<const char*>(str)));
    }
// #if __cplusplus >= 202002L
//     else if constexpr (std::is_same_v<From, const char8_t*> || std::is_same_v<From, char8_t*>) {
//         u32str = utf8_to_utf32(reinterpret_cast<const char*>(str),
//                                strlen(reinterpret_cast<const char*>(str)));
//     }
// #endif
    else if constexpr (std::is_same_v<From, const char16_t*> || std::is_same_v<From, char16_t*>) {
        size_t len = 0;
        while (str[len] != 0) len++;
        u32str = utf16_to_utf32(str, len);
    } else if constexpr (std::is_same_v<From, const char32_t*> || std::is_same_v<From, char32_t*>) {
        size_t len = 0;
        while (str[len] != 0) len++;
        u32str.assign(str, str + len);
    } else if constexpr (std::is_same_v<From, const wchar_t*> || std::is_same_v<From, wchar_t*>) {
        size_t len = 0;
        while (str[len] != 0) len++;
        if constexpr (sizeof(wchar_t) == 2) {
            u32str = utf16_to_utf32(reinterpret_cast<const char16_t*>(str), len);
        } else {
            u32str.assign(reinterpret_cast<const char32_t*>(str),
                          reinterpret_cast<const char32_t*>(str) + len);
        }
    } else if constexpr (std::is_same_v<From, std::string>) {
        u32str = utf8_to_utf32(str.data(), str.length());
    } else if constexpr (std::is_same_v<From, std::u8string>) {
        u32str = utf8_to_utf32(reinterpret_cast<const char*>(str.data()), str.length());
    } else if constexpr (std::is_same_v<From, std::wstring>) {
        if constexpr (sizeof(wchar_t) == 2) {
            u32str = utf16_to_utf32(reinterpret_cast<const char16_t*>(str.data()), str.length());
        } else {
            u32str.assign(str.begin(), str.end());
        }
    } else if constexpr (std::is_same_v<From, std::u16string>) {
        u32str = utf16_to_utf32(str.data(), str.length());
    } else if constexpr (std::is_same_v<From, std::u32string>) {
        u32str = str;
    }

    // UTF-32转换为目标格式
    To result;
    std::string temp;
    if constexpr (std::is_same_v<To, const char*> || std::is_same_v<To, char*>) {
        std::string temp = utf32_to_utf8(u32str);
        char* result = new char[temp.length() + 1];
        std::memcpy(result, temp.data(), temp.length());
        result[temp.length()] = '\0';
        return result;
    } else if constexpr (std::is_same_v<To, const signed char*> || std::is_same_v<To, signed char*>) {
        std::string temp = utf32_to_utf8(u32str);
        signed char* result = new signed char[temp.length() + 1];
        std::memcpy(result, temp.data(), temp.length());
        result[temp.length()] = '\0';
        return result;
    } else if constexpr (std::is_same_v<To, const unsigned char*> || std::is_same_v<To, unsigned char*>) {
        std::string temp = utf32_to_utf8(u32str);
        unsigned char* result = new unsigned char[temp.length() + 1];
        std::memcpy(result, temp.data(), temp.length());
        result[temp.length()] = '\0';
        return result;
    }
// #if __cplusplus >= 202002L
//     else if constexpr (std::is_same_v<To, const char8_t*> || std::is_same_v<To, char8_t*>) {
//         std::string temp = utf32_to_utf8(u32str);
//         char8_t* result = new char8_t[temp.length() + 1];
//         std::memcpy(result, temp.data(), temp.length());
//         result[temp.length()] = '\0';
//         return result;
//     }
// #endif
    else if constexpr (std::is_same_v<To, const char16_t*> || std::is_same_v<To, char16_t*>) {
        std::u16string temp = utf32_to_utf16(u32str);
        char16_t* result = new char16_t[temp.length() + 1];
        std::memcpy(result, temp.data(), temp.length() * sizeof(char16_t));
        result[temp.length()] = '\0';
        return result;
    } else if constexpr (std::is_same_v<To, const char32_t*> || std::is_same_v<To, char32_t*>) {
        char32_t* result = new char32_t[u32str.length() + 1];
        std::memcpy(result, u32str.data(), u32str.length() * sizeof(char32_t));
        result[u32str.length()] = '\0';
        return result;
    } else if constexpr (std::is_same_v<To, const wchar_t*> || std::is_same_v<To, wchar_t*>) {
        if constexpr (sizeof(wchar_t) == 2) {
            std::u16string temp = utf32_to_utf16(u32str);
            wchar_t* result = new wchar_t[temp.length() + 1];
            std::memcpy(result, temp.data(), temp.length() * sizeof(wchar_t));
            result[temp.length()] = '\0';
            return result;
        } else {
            wchar_t* result = new wchar_t[u32str.length() + 1];
            std::memcpy(result, u32str.data(), u32str.length() * sizeof(wchar_t));
            result[u32str.length()] = '\0';
            return result;
        }
    } else if constexpr (std::is_same_v<To, std::string>) {
        temp = utf32_to_utf8(u32str);
        result.assign(temp.begin(), temp.end());
    } else if constexpr (std::is_same_v<To, std::wstring>) {
        if constexpr (sizeof(wchar_t) == 2) {
            auto u16str = utf32_to_utf16(u32str);
            result.assign(reinterpret_cast<const wchar_t*>(u16str.data()), u16str.length());
        } else {
            result.assign(u32str.begin(), u32str.end());
        }
    }/* else if constexpr (std::is_same_v<To, std::u8string>) {
        temp = utf32_to_utf8(u32str);
        result.assign(reinterpret_cast<const char8_t*>(temp.data()), temp.length());
    }*/ else if constexpr (std::is_same_v<To, std::u16string>) {
        result = utf32_to_utf16(u32str);
    } else if constexpr (std::is_same_v<To, std::u32string>) {
        result = u32str;
    }
    return result;
}

template<typename To, typename From>
To string_cast(const From& value) {
    static_assert(is_string_v<To>, "string_cast: Target type must be string.");
    if constexpr (std::is_same_v<From, To>) return value;

    if constexpr (std::is_same_v<From, std::nullptr_t>)
        return string_convert<To>("");
    if constexpr (std::is_same_v<From, bool>)
        return value ? string_convert<To>("true") : string_convert<To>("false");
    if constexpr (is_character_v<From>)
        return string_convert<To>(std::string(1, value));
    if constexpr (std::is_arithmetic_v<From>)
        return string_convert<To>(std::to_string(value));
    if constexpr (is_string_v<From>)
        return string_convert<To>(value);
    if constexpr (std::is_array_v<From>)
        return string_convert<To>(value);
    // if constexpr (is_container_v<From>)
    //     return "";
    if constexpr (is_pair_v<From>)
        return string_cast<To>(value.first);
    if constexpr (std::is_same_v<From, Variant>)
        return value.template to<To>();

    //std::cerr << "Error: string type conversion failure, from: " << _typename<From>() << " to: " << _typename<To>() << std::endl;
    if constexpr (std::is_same_v<To, const char*>)// || std::is_same_v<To, char*>)
        return "";
    if constexpr (std::is_same_v<To, wchar_t*>)// || std::is_same_v<To, wchar_t*>)
        return L"";
// #if __cplusplus >= 202002L
//     if constexpr (std::is_same_v<To, const char8_t*>)// || std::is_same_v<To, char8_t*>)
//         return u8"";
// #endif
    if constexpr (std::is_same_v<To, const char16_t*>)// || std::is_same_v<To, char16_t*>)
        return u"";
    if constexpr (std::is_same_v<To, const char32_t*>)// || std::is_same_v<To, char32_t*>)
        return U"";
    return To();
}

// To用于兼容其他cast函数
template<typename To, typename From>
View view_cast(const From& value) {
    static_assert(std::is_same_v<To, View>, "view_cast: Target type must be View.");
    if constexpr (std::is_same_v<From, View>) return value;

    if constexpr (std::is_same_v<From, std::nullptr_t>)  // 无效
        return View("nullptr");
    if constexpr (std::is_same_v<From, bool>)
        return value ? View("true") : View("false");
    if constexpr (is_character_v<From>)
        return View('\'' + string_convert<std::string>(std::string(1, value)) + '\'');
    if constexpr (std::is_arithmetic_v<From>)
        return View(std::to_string(value));
    if constexpr (is_string_v<From>)
        return View(std::string("\"") + string_convert<std::string>(value) + "\"");
    if constexpr (std::is_same_v<From, Id>)
        return View(std::string("\"") + value.toString() + "\"");

    if constexpr (std::is_pointer_v<From> || is_smart_pointer_v<From>)
        return View(View("*") + view_cast<View>(*value));

    if constexpr (is_sequence_container_v<From>
                  || is_set_v<From>
                  || is_multiset_v<From>
                  || is_unordered_set_v<From>
                  || is_unordered_multiset_v<From>
                  ) {
        View result = "[";
        for (auto it = value.begin(); it != value.end(); ++it) {
            result += view_cast<View>(*it);
            if (std::next(it) != value.end()) {
                result += ", ";
            }
        }
        result += "]";
        return result;
    }
    if constexpr (is_associative_container_v<From>) {  // maps only
        View result = "{";
        for (auto it = value.begin(); it != value.end(); ++it) {
            result += view_cast<View>(it->first) + ": ";
            result += view_cast<View>(it->second);
            if (std::next(it) != value.end()) {
                result += ", ";
            }
        }
        result += "}";
        return result;
    }
    if constexpr (is_container_adapters_v<From>) {
        std::vector<typename From::value_type> temp;
        while (!value.empty()) {
            if constexpr (is_queue_v<From>) {
                temp.push_back(value.front());
            } else {
                temp.push_back(value.top());
            }
            value.pop();
        }
        View result = "[";
        for (auto it = value.begin(); it != value.end(); ++it) {
            result += view_cast<View>(*it) + ": ";
            value.push(*it);
            if (std::next(it) != value.end()) {
                result += ", ";
            }
        }
        result += "]";
        return result;
    }
    if constexpr (is_pair_v<From>) {
        return view_cast<View>(value.first);
    }
    if constexpr (std::is_same_v<From, Variant>) {
        return value.template to<View>();
    }
    std::cerr << "view_cast: Conversion failure, source type: " << typeid(From).name() << std::endl;
    return View();
}


// To用于兼容其他cast函数
template<typename To, typename From>
Id id_cast(const From& value) {
    static_assert(std::is_same_v<To, Id>, "id_cast: Target type must be Id.");
    if constexpr (std::is_same_v<From, Id>) return value;

    if constexpr (is_character_v<From>)
        return Id(string_convert<std::string>(std::string(1, value)));
    if constexpr (is_string_v<From>)
        return Id(string_convert<std::string>(value));

    std::cerr << "view_cast: Conversion failure, source type: " << typeid(From).name() << std::endl;
    return Id();
}

template<typename To, typename From>
std::vector<To> vector_convert(const std::vector<From>& value, const std::function<To(From)>& func) {
    std::vector<To> result;
    for (auto it = value.begin(); it != value.end(); ++it) {
        result.push_back(func(*it));
    }
    return result;
}

template<typename To, typename From>
std::vector<To> variant_vector_convert(const std::vector<From>& value) {
    static_assert(std::is_same_v<To, Variant> || std::is_same_v<From, Variant>,
                  "variant_vector_convert: Target or Source type must have one as a Variant.");

    std::vector<To> result;
    if constexpr (std::is_same_v<To, Variant>) {
        for (auto it = value.begin(); it != value.end(); ++it)
            result.push_back(Variant(*it));
    } else {
        for (auto it = value.begin(); it != value.end(); ++it)
            result.push_back((*it).template value<To>());
    }
    return result;
}

template<typename To, typename From>
To vector_cast(const From& value) {
    static_assert(is_vector_v<To>, "vector_cast: Target type must be std::vector<T>.");
    if constexpr (std::is_same_v<From, To>) return value;

    if constexpr (std::is_same_v<From, Variant>)
        return value.template to<To>();
    if constexpr (std::is_same_v<From, std::vector<Variant>>) {
        To result;
        for (const auto& v : value) {
            result.push_back(v.template to<vector_element_t<To>>());
        }
        return result;
    }

    if constexpr(is_vector_v<From>) {
#define VECTOR_FUNC(TO, FROM, FUNC) \
std::function<vector_element_t<To>(vector_element_t<From>)>( \
    FUNC<vector_element_t<To>, vector_element_t<From>>)
        if constexpr (std::is_arithmetic_v<vector_element_t<To>>)
            return vector_convert<vector_element_t<To>>(value, VECTOR_FUNC(To, From, numeric_cast));
        if constexpr (is_string_v<vector_element_t<To>>)
            return vector_convert<vector_element_t<To>>(value, VECTOR_FUNC(To, From, string_cast));
        if constexpr (std::is_same_v<vector_element_t<To>, View>)
            return vector_convert<vector_element_t<To>>(value, VECTOR_FUNC(To, From, view_cast));
        if constexpr (std::is_same_v<vector_element_t<To>, Variant>)
            return variant_vector_convert<vector_element_t<To>>(value);
        std::cerr << "vector_cast: Conversion target type(vector) failure." << std::endl;
        return To();
    }

    // 目标类型为 std::vector<Variant>, 且源类型不为容器
    if constexpr (std::is_same_v<To, std::vector<Variant>>) {
        return { Variant(value) };
    }

    if constexpr (std::is_arithmetic_v<vector_element_t<To>>) {
        return { numeric_cast<vector_element_t<To>>(value) };
    }
    if constexpr (is_string_v<vector_element_t<To>>) {
        return { string_cast<vector_element_t<To>>(value) };
    }
    // if constexpr (std::is_same_v<To, std::pair<std::string, Variant>>) {
    //     std::pair<std::string, Variant> pair;
    //     const auto vec = std::any_cast<std::vector<From>>(value);
    //     pair.first = Variant(vec[0]).toString();
    //     pair.second = vec[1];
    //     return pair;
    // }

    std::cerr << "Error: std::vector type conversion failure, from: " << typeid(From).name() << " to: " << typeid(To).name() << std::endl;
    return To();
}

// template <typename To, typename From>
// To container_convert(const From& source);

// // 专门化: vector 到 list
// template <>
// std::list<int> container_convert<std::list<int>, std::vector<int>>(const std::vector<int>& source) {
//     return std::list<int>(source.begin(), source.end());
// }

// // 专门化: list 到 vector
// template <>
// std::vector<int> container_convert<std::vector<int>, std::list<int>>(const std::list<int>& source) {
//     return std::vector<int>(source.begin(), source.end());
// }

// // 专门化: set 到 vector
// template <>
// std::vector<int> container_convert<std::vector<int>, std::set<int>>(const std::set<int>& source) {
//     return std::vector<int>(source.begin(), source.end());
// }

// // 专门化: unordered_set 到 vector
// template <>
// std::vector<int> container_convert<std::vector<int>, std::unordered_set<int>>(const std::unordered_set<int>& source) {
//     return std::vector<int>(source.begin(), source.end());
// }



// vector array deque list forward_list

template <typename To, typename From>
To pair_cast(const From& value) {
    static_assert(pair_traits<To>::is_pair,
                  "pair_cast: Target type must be pair type.");
    if constexpr (std::is_same_v<From, To>) return value;

    using first_t = typename pair_traits<To>::first_type;
    using second_t = typename pair_traits<To>::second_type;

    if constexpr (pair_traits<From>::is_pair) {
        return std::pair<first_t, second_t>(
                          basic_cast<first_t>(value.first),
                          basic_cast<second_t>(value.second)
            );
    }
    if constexpr (std::is_arithmetic_v<From>) {
        return  std::pair<first_t, second_t>(
            basic_cast<first_t>(value), second_t());
    }
    if constexpr (is_string_v<From>) {
        return  std::pair<first_t, second_t>(
            basic_cast<first_t>(value), second_t());
    }
    if constexpr (is_vector_v<From>) {
        return  std::pair<first_t, second_t>(
            !value.empty() ? basic_cast<first_t>(*value.begin()) : first_t(),
            value.size() > 1 ? basic_cast<second_t>(*(value.begin()+1)) : second_t());
    }
    if constexpr (is_map_v<From>) {
        return  std::pair<first_t, second_t>(
            !value.empty() ? basic_cast<first_t>(value.begin()->first) : first_t(),
            value.size() > 1 ? basic_cast<second_t>(value.begin()->first) : second_t());
    }
    if constexpr (std::is_same_v<From, Variant>) {
        return value.template to<To>();
    }
    return To();
}

template <typename To, typename From>
To map_cast(const From& value) {
    To target;

    using To_v = typename To::value_type;

    if constexpr (is_sequence_container_v<From>) {
        using From_v = typename From::value_type;
        if constexpr (is_forward_list_v<From>) {
            for (const auto& item : value) {
                target.insert_after(target.end(), basic_cast<To_v>(item));
            }
        } else {
            for (const auto& item : value) {
                target.insert(target.end(), basic_cast<To_v>(item));
            }
        }
        return target;
    }

    if constexpr (is_set_v<From> || is_set_v<From>) {
        for (const auto& item : value) {
            target.insert(target.end(), basic_cast<typename To::value_type>(item));
        }
    }
    //else if constexpr (std::is_same_v<From, std::map<From_v, typename From::mapped_type>>) {
    else if constexpr (is_map_v<From>) {
        using From_v = typename From::value_type;
        for (const auto& pair : value) {
            target.insert(target.end(), pair_cast<To_v>(pair));
        }
    } else if constexpr (pair_traits<From>::is_pair) {
        target.insert(target.end(), pair_cast<To_v>(value));
    } else if constexpr (std::is_same_v<From, Variant>) {
        return value.template to<To>();
    }
    return target;
}

#endif // TYPECAST_H
