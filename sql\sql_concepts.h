#ifndef DATABASE_SQL_CONCEPTS_H
#define DATABASE_SQL_CONCEPTS_H

#include <concepts>
#include <string>
#include <string_view>
#include <type_traits>

namespace database {

// Forward declarations
class SqlDriver;
enum class SqlObjectMode;

/**
 * @brief Concept for types that can be used as SQL identifiers
 */
template<typename T>
concept SqlIdentifier = std::convertible_to<T, std::string_view> &&
                       requires(T t) {
                           { t.empty() } -> std::convertible_to<bool>;
                       };

/**
 * @brief Concept for SQL database objects (simplified API)
 */
template<typename T>
concept SqlDatabaseObject = requires(T t, const T ct, SqlDriver* driver) {
    // Basic properties
    { ct.name() } -> std::convertible_to<std::string_view>;
    { ct.schema() } -> std::convertible_to<std::string_view>;
    { ct.alias() } -> std::convertible_to<std::string_view>;
    { ct.hasSchema() } -> std::convertible_to<bool>;
    { ct.hasAlias() } -> std::convertible_to<bool>;

    // Fluent setters
    { t.setName(std::string_view{}) } -> std::same_as<T&>;
    { t.setSchema(std::string_view{}) } -> std::same_as<T&>;
    { t.setAlias(std::string_view{}) } -> std::same_as<T&>;
    { t.as(std::string_view{}) } -> std::same_as<T&>;
    { t.setDriver(driver) } -> std::same_as<T&>;

    // SQL generation
    { ct.qualifiedName() } -> std::convertible_to<std::string>;
    { ct.toSql() } -> std::convertible_to<std::string>;
};

/**
 * @brief Concept for SQL objects that can generate SQL statements
 */
template<typename T>
concept SqlGenerator = requires(const T ct) {
    { ct.toSql() } -> std::convertible_to<std::string>;
    { ct.qualifiedName() } -> std::convertible_to<std::string>;
};

/**
 * @brief Concept for SQL objects that support metadata loading
 */
template<typename T>
concept MetadataLoadable = requires(T t, const T ct) {
    { ct.isMetadataLoaded() } -> std::convertible_to<bool>;
    { t.reload() } -> std::same_as<T&>;
    { t.preloadMetadata() } -> std::same_as<T&>;
};

/**
 * @brief Concept for SQL objects that have schema support
 */
template<typename T>
concept SchemaAware = requires(T t, const T ct) {
    { ct.schema() } -> std::convertible_to<std::string_view>;
    { ct.hasSchema() } -> std::convertible_to<bool>;
    { t.setSchema(std::string_view{}) } -> std::same_as<T&>;
};

/**
 * @brief Concept for SQL objects that support aliasing
 */
template<typename T>
concept Aliasable = requires(T t, const T ct) {
    { ct.alias() } -> std::convertible_to<std::string_view>;
    { ct.hasAlias() } -> std::convertible_to<bool>;
    { t.setAlias(std::string_view{}) } -> std::same_as<T&>;
    { t.as(std::string_view{}) } -> std::same_as<T&>;
};

/**
 * @brief Concept for data containers (like SqlRow)
 */
template<typename T>
concept SqlDataContainer = requires(T t, const T ct) {
    { ct.size() } -> std::convertible_to<size_t>;
    { ct.empty() } -> std::convertible_to<bool>;
    { t.clear() } -> std::same_as<void>;
};

/**
 * @brief Type trait to check if a type is a SQL metadata structure
 */
template<typename T>
struct is_sql_metadata : std::false_type {};

template<typename T>
constexpr bool is_sql_metadata_v = is_sql_metadata<T>::value;

/**
 * @brief Concept for SQL metadata structures
 */
template<typename T>
concept SqlMetadata = is_sql_metadata_v<T> && requires(const T ct) {
    { ct.name } -> std::convertible_to<std::string_view>;
};

} // namespace database

#endif // DATABASE_SQL_CONCEPTS_H
