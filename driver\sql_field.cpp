#include "sql_field.h"

namespace database {

SqlField::SqlField(std::string_view name, const Variant& value)
    : m_name(name), m_value(value) {
}

bool SqlField::operator==(const SqlField& other) const noexcept {
    // Compare names directly and values by string representation
    return m_name == other.m_name && m_value.to<std::string>() == other.m_value.to<std::string>();
}

bool SqlField::operator!=(const SqlField& other) const noexcept {
    return !(*this == other);
}

void SqlField::setReadOnly(bool readOnly) noexcept {
    m_readOnly = readOnly;
}

bool SqlField::isReadOnly() const noexcept {
    return m_readOnly;
}

} // namespace database
