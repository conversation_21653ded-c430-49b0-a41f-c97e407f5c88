#ifndef DATABASE_SQL_OBJECT_BASE_H
#define DATABASE_SQL_OBJECT_BASE_H

#include <string>
#include <string_view>
#include <memory>
#include <stdexcept>
#include <format>

#include "sql_enums.h"

namespace database {

// Forward declarations
class SqlDriver;

/**
 * @brief Base class for SQL database objects with dual-mode support
 *
 * This class provides common functionality for SQL objects that operate
 * in both Builder and Metadata modes, including basic properties,
 * mode management, and resource optimization.
 */
class SqlObjectBase {
public:
    /**
     * @brief Core properties structure for lightweight storage
     */
    struct CoreProperties {
        std::string name;
        std::string schema;
        std::string alias;

        // Default constructors (not constexpr due to std::string)
        CoreProperties() = default;
        explicit CoreProperties(std::string_view n) : name(n) {}
        CoreProperties(std::string_view n, std::string_view s) : name(n), schema(s) {}
        CoreProperties(std::string_view n, std::string_view s, std::string_view a)
            : name(n), schema(s), alias(a) {}
    };

protected:
    /**
     * @brief Protected constructor for derived classes
     * @param mode Initial operation mode
     * @param props Core properties
     * @param driver Database driver (optional, required for Metadata mode)
     */
    SqlObjectBase(SqlObjectMode mode, CoreProperties props = {}, SqlDriver* driver = nullptr) noexcept
        : m_props(std::move(props)), m_mode(mode), m_driver(driver) {}

protected:
    // Internal Mode Management (hidden from public API)

    /**
     * @brief Get the current operation mode (internal use only)
     * @return The operation mode
     */
    [[nodiscard]] SqlObjectMode mode() const noexcept { return m_mode; }

    /**
     * @brief Check if the object is in Builder mode (internal use only)
     * @return True if in Builder mode, false otherwise
     */
    [[nodiscard]] bool isBuilderMode() const noexcept {
        return m_mode == SqlObjectMode::Builder;
    }

    /**
     * @brief Check if the object is in Metadata mode (internal use only)
     * @return True if in Metadata mode, false otherwise
     */
    [[nodiscard]] bool isMetadataMode() const noexcept {
        return m_mode == SqlObjectMode::Metadata;
    }

public:

    // Basic Properties (inline getters for performance)

    /**
     * @brief Get the object name
     * @return The object name
     */
    [[nodiscard]] const std::string& name() const noexcept { return m_props.name; }

    /**
     * @brief Get the schema name
     * @return The schema name
     */
    [[nodiscard]] const std::string& schema() const noexcept { return m_props.schema; }

    /**
     * @brief Get the alias
     * @return The alias
     */
    [[nodiscard]] const std::string& alias() const noexcept { return m_props.alias; }

    /**
     * @brief Check if the object has a schema
     * @return True if the object has a schema, false otherwise
     */
    [[nodiscard]] bool hasSchema() const noexcept { return !m_props.schema.empty(); }

    /**
     * @brief Check if the object has an alias
     * @return True if the object has an alias, false otherwise
     */
    [[nodiscard]] bool hasAlias() const noexcept { return !m_props.alias.empty(); }

    /**
     * @brief Get the database driver
     * @return Pointer to the database driver, or nullptr if not set
     */
    [[nodiscard]] SqlDriver* driver() const noexcept { return m_driver; }

    // Setters (non-inline, implemented in .cpp)

    /**
     * @brief Set the object name
     * @param name The object name
     * @return Reference to this object for method chaining
     */
    SqlObjectBase& setName(std::string_view name);

    /**
     * @brief Set the schema name
     * @param schema The schema name
     * @return Reference to this object for method chaining
     */
    SqlObjectBase& setSchema(std::string_view schema);

    /**
     * @brief Set the alias
     * @param alias The alias
     * @return Reference to this object for method chaining
     */
    SqlObjectBase& setAlias(std::string_view alias);

    /**
     * @brief Set the alias (fluent interface)
     * @param alias The alias
     * @return Reference to this object for method chaining
     */
    SqlObjectBase& as(std::string_view alias) { return setAlias(alias); }

    /**
     * @brief Set the database driver (automatically switches to appropriate mode)
     * @param driver The database driver (nullptr switches to Builder mode)
     * @return Reference to this object for method chaining
     */
    SqlObjectBase& setDriver(SqlDriver* driver);

    // Utility methods (non-inline, implemented in .cpp)

    /**
     * @brief Get the qualified name (schema.name or just name)
     * @return The qualified name
     */
    [[nodiscard]] std::string qualifiedName() const;

    /**
     * @brief Generate SQL representation with optional alias
     * @return The SQL string
     */
    [[nodiscard]] std::string toSql() const;

protected:
    // Helper methods for derived classes

    /**
     * @brief Require Metadata mode and valid driver (internal use only)
     * Automatically attempts to use metadata operations if driver is available
     * @throws std::runtime_error if driver is null and metadata access is required
     */
    void requireMetadataMode() const;

    /**
     * @brief Check if metadata operations are available (internal use only)
     * @return True if driver is available for metadata operations
     */
    bool hasMetadataCapability() const noexcept { return m_driver != nullptr; }

    /**
     * @brief Invalidate cached metadata (for derived classes)
     */
    virtual void invalidateCache() {}

    /**
     * @brief Get mutable access to core properties (for derived classes)
     * @return Reference to core properties
     */
    CoreProperties& mutableProps() noexcept { return m_props; }

private:
    // Internal mode switching (automatic, based on driver availability)

    /**
     * @brief Switch to Builder mode (internal use only)
     */
    void switchToBuilderMode();

    /**
     * @brief Switch to Metadata mode (internal use only)
     * @param driver Database driver for metadata operations
     */
    void switchToMetadataMode(SqlDriver* driver);

private:
    CoreProperties m_props;
    SqlObjectMode m_mode;
    SqlDriver* m_driver;
};

} // namespace database

#endif // DATABASE_SQL_OBJECT_BASE_H
