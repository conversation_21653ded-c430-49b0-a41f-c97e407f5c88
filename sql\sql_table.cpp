﻿#include "sql_table.h"

#include <format>
#include <stdexcept>
#include <algorithm>

#include "driver/sql_driver.h"
#include "sql_index.h"
#include "sql_column.h"

namespace database {

// Catalog management (not in base class)
const std::string& SqlTable::catalog() const noexcept {
    return m_catalog;
}

SqlTable& SqlTable::setCatalog(std::string_view catalog) {
    m_catalog = catalog;
    if (isMetadataMode()) {
        invalidateCache();  // Clear cached metadata when catalog changes
    }
    return *this;
}

SqlColumn SqlTable::column(std::string_view columnName) const {
    if (hasMetadataCapability()) {
        // If driver is available, try to find the column in loaded metadata
        const auto& cols = columns();
        auto it = std::find_if(cols.begin(), cols.end(),
                               [columnName](const auto& col) {
                                   return col.name() == columnName;
                               });

        if (it != cols.end()) {
            return *it;
        }

        // If not found in metadata, create a new column with driver
        return SqlColumn(columnName, this, driver());
    } else {
        // No driver available, create a lightweight column
        return SqlColumn(columnName, this);
    }
}

bool SqlTable::hasColumn(std::string_view columnName) const {
    requireMetadataMode();
    const auto& cols = columns();
    return std::ranges::any_of(cols, [columnName](const auto& col) {
        return col.name() == columnName;
    });
}

const std::vector<SqlColumn>& SqlTable::columns() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->columns;
    }

    static const std::vector<SqlColumn> empty;
    return empty;
}

std::vector<std::string> SqlTable::primaryKeyColumns() const {
    requireMetadataMode();
    std::vector<std::string> pkColumns;

    for (const auto& col : columns()) {
        if (col.isPrimaryKey()) {
            pkColumns.push_back(col.name());
        }
    }

    return pkColumns;
}

bool SqlTable::hasPrimaryKey() const {
    requireMetadataMode();
    return std::any_of(columns().begin(), columns().end(),
                       [](const auto& col) { return col.isPrimaryKey(); });
}

const std::vector<SqlIndex>& SqlTable::indexes() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->indexes;
    }

    static const std::vector<SqlIndex> empty;
    return empty;
}

const SqlIndex* SqlTable::index(std::string_view indexName) const {
    requireMetadataMode();
    const auto& idxs = indexes();
    auto it = std::find_if(idxs.begin(), idxs.end(),
                           [indexName](const auto& idx) {
                               return idx.name() == indexName;
                           });

    return (it != idxs.end()) ? &(*it) : nullptr;
}

bool SqlTable::hasIndex(std::string_view indexName) const {
    requireMetadataMode();
    return index(indexName) != nullptr;
}

SqlColumn SqlTable::all() const {
    // Available in both modes - create a wildcard column
    if (isMetadataMode()) {
        return SqlColumn::metadata("*", this, m_driver);
    } else {
        return SqlColumn::builder("*", this);
    }
}

std::string SqlTable::qualifiedName() const {
    if (m_name.empty()) {
        return {};
    }

    // If we have an alias, use it as the qualified name
    if (hasAlias()) {
        return m_alias;
    }

    // If we have a schema, use schema.name format
    if (hasSchema()) {
        return std::format("{}.{}", m_schema, m_name);
    }

    // Otherwise just use the name
    return m_name;
}

SqlTable& SqlTable::reload() {
    invalidateCache();
    return *this;
}

SqlTable& SqlTable::preloadMetadata() {
    ensureMetadataLoaded();
    return *this;
}

std::string SqlTable::selectSql(const std::vector<std::string>& columns) const {
    std::ostringstream sql;
    sql << "SELECT ";

    if (columns.empty()) {
        sql << "*";
    } else {
        for (size_t i = 0; i < columns.size(); ++i) {
            if (i > 0) sql << ", ";
            sql << columns[i];
        }
    }

    sql << " FROM " << qualifiedName();

    return sql.str();
}

std::string SqlTable::createTableSql(bool ifNotExists) const {
    ensureMetadataLoaded();

    if (!m_metadata || m_columns.empty()) {
        return "";
    }

    std::ostringstream sql;
    sql << "CREATE TABLE ";

    if (ifNotExists) {
        sql << "IF NOT EXISTS ";
    }

    sql << qualifiedName() << " (\n";

    // Add columns
    for (size_t i = 0; i < m_columns.size(); ++i) {
        if (i > 0) sql << ",\n";
        sql << "    " << m_columns[i].definitionSql();
    }

    sql << "\n)";

    return sql.str();
}

std::string SqlTable::dropTableSql(bool ifExists) const {
    std::ostringstream sql;
    sql << "DROP TABLE ";

    if (ifExists) {
        sql << "IF EXISTS ";
    }

    sql << qualifiedName();

    return sql.str();
}

bool SqlTable::exists() const {
    requireMetadataMode();
    return m_driver->tableExists(*this);
}

std::optional<SqlObjectType> SqlTable::type() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->type;
    }
    return std::nullopt;
}

const std::string& SqlTable::comment() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->comment;
    }

    static const std::string empty;
    return empty;
}

const std::string& SqlTable::createSql() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->createSql;
    }

    static const std::string empty;
    return empty;
}

std::string SqlTable::toSql() const {
    if (m_name.empty()) {
        return {};
    }

    std::string tableName = m_name;

    // Add schema prefix if present
    if (hasSchema()) {
        tableName = std::format("{}.{}", m_schema, m_name);
    }

    // Add alias if present
    if (hasAlias()) {
        return std::format("{} AS {}", tableName, m_alias);
    }

    return tableName;
}

const SqlTable::SqlTableMetadata* SqlTable::metadata() const {
    ensureMetadataLoaded();
    return m_metadata.get();
}

void SqlTable::loadMetadata() const {
    if (!driver() || name().empty() || !isMetadataMode()) {
        return;
    }

    try {
        // Delegate to driver for actual metadata loading
        auto metadata = driver()->getTableMetadata(*this);
        m_metadata = std::make_shared<SqlTableMetadata>(std::move(metadata));
        m_metadataLoaded = true;
    } catch (const std::exception&) {
        // If loading fails, create empty metadata and mark as loaded
        m_metadata = std::make_shared<SqlTableMetadata>();
        // Note: name, schema are now in base class, only catalog is stored here
        m_metadata->catalog = m_catalog;
        m_metadataLoaded = true;
    }
}

void SqlTable::invalidateCache() {
    // Only clear cache if in metadata mode
    if (isMetadataMode()) {
        m_metadataLoaded = false;
        m_metadata.reset();
    }
}

void SqlTable::ensureMetadataLoaded() const {
    if (!m_metadataLoaded) {
        loadMetadata();
    }
}

} // namespace database
