﻿#ifndef DATABASE_QUERY_BUILDER_H
#define DATABASE_QUERY_BUILDER_H

#include <string_view>
#include <tuple>

#include "sql_table.h"
#include "sql_column.h"
#include "sql_row.h"
#include "select_builder.h"
#include "insert_builder.h"
#include "update_builder.h"
#include "delete_builder.h"

namespace database {

/**
 * @brief Factory class for creating SQL query builders
 *
 * This class provides static methods for creating various types of SQL query builders.
 * It serves as the main entry point for the SQL builder API.
 */
class QueryBuilder {
public:
    /**
     * @brief Create a SELECT query builder with columns using variadic templates
     * @tparam Args Types of the columns (must be convertible to Column)
     * @param columns The columns to select
     * @return A SelectBuilder instance
     */
    template<ColumnConvertible... Args>
    [[nodiscard]] static SelectBuilder select(Args&&... columns) {
        SelectBuilder builder;
        if constexpr (sizeof...(columns) > 0) {
            builder.select(std::forward<Args>(columns)...);
        }
        return builder;
    }

    /**
     * @brief Create a SELECT query builder with columns
     * @param columns The columns to select
     * @return A SelectBuilder instance
     */
    [[nodiscard]] static SelectBuilder select(std::initializer_list<Column> columns = {}) {
        SelectBuilder builder;
        if (columns.size() > 0) {
            builder.select(std::vector<Column>(columns));
        }
        return builder;
    }

    /**
     * @brief Create a SELECT query builder with a vector of columns
     * @param columns The columns to select
     * @return A SelectBuilder instance
     */
    [[nodiscard]] static SelectBuilder select(const std::vector<Column>& columns) {
        SelectBuilder builder;
        builder.select(columns);
        return builder;
    }

    /**
     * @brief Create a SELECT query builder with all columns from a table
     * @param table The table to select from
     * @return A SelectBuilder instance
     */
    [[nodiscard]] static SelectBuilder selectAll(const Table& table) {
        SelectBuilder builder;
        builder.selectAll().from(table);
        return builder;
    }

    /**
     * @brief Create an INSERT query builder for a table
     * @param table The table to insert into
     * @return An InsertBuilder instance
     */
    [[nodiscard]] static InsertBuilder insert(const Table& table) {
        InsertBuilder builder;
        builder.into(table);
        return builder;
    }

    /**
     * @brief Create an INSERT query builder for a table with a row
     * @param table The table to insert into
     * @param row The row to insert
     * @return An InsertBuilder instance
     */
    [[nodiscard]] static InsertBuilder insert(const Table& table, const Row& row) {
        InsertBuilder builder;
        builder.into(table).setRow(row);
        return builder;
    }

    /**
     * @brief Create an INSERT query builder for a table with columns and values using variadic templates
     * @tparam ColArgs Types of the columns (must be convertible to Column)
     * @tparam ValArgs Types of the values (must be convertible to Variant)
     * @param table The table to insert into
     * @param cols The columns to insert into
     * @param vals The values to insert
     * @return An InsertBuilder instance
     */
    template<typename... ColArgs, typename... ValArgs>
    [[nodiscard]] static InsertBuilder insert(const Table& table,
                                              const std::tuple<ColArgs...>& cols,
                                              const std::tuple<ValArgs...>& vals) {
        InsertBuilder builder;
        builder.into(table);

        // Apply columns from tuple
        std::apply([&builder](const auto&... columns) {
            builder.columns(columns...);
        }, cols);

        // Apply values from tuple
        std::apply([&builder](const auto&... values) {
            builder.values(values...);
        }, vals);

        return builder;
    }

    /**
     * @brief Create an INSERT query builder for a table with column-value pairs using variadic templates
     * @tparam Args Types of the column-value pairs (must be convertible to std::pair<Column, Variant>)
     * @param table The table to insert into
     * @param columnValues The column-value pairs to set
     * @return An InsertBuilder instance
     */
    template<ColumnValuePair... Args>
    [[nodiscard]] static InsertBuilder insertValues(const Table& table, Args&&... columnValues) {
        InsertBuilder builder;
        builder.into(table).set(std::forward<Args>(columnValues)...);
        return builder;
    }

    /**
     * @brief Create an UPDATE query builder for a table
     * @param table The table to update
     * @return An UpdateBuilder instance
     */
    [[nodiscard]] static UpdateBuilder update(const Table& table) {
        return UpdateBuilder(table);
    }

    /**
     * @brief Create an UPDATE query builder for a table with a row
     * @param table The table to update
     * @param row The row with values to update
     * @return An UpdateBuilder instance
     */
    [[nodiscard]] static UpdateBuilder update(const Table& table, const Row& row) {
        UpdateBuilder builder(table);
        builder.setRow(row);
        return builder;
    }

    /**
     * @brief Create an UPDATE query builder for a table with column-value pairs using variadic templates
     * @tparam Args Types of the column-value pairs (must be convertible to std::pair<Column, Variant>)
     * @param table The table to update
     * @param columnValues The column-value pairs to set
     * @return An UpdateBuilder instance
     */
    template<ColumnValuePair... Args>
    [[nodiscard]] static UpdateBuilder updateValues(const Table& table, Args&&... columnValues) {
        UpdateBuilder builder(table);
        builder.set(std::forward<Args>(columnValues)...);
        return builder;
    }

    /**
     * @brief Create a DELETE query builder for a table
     * @param table The table to delete from
     * @return A DeleteBuilder instance
     */
    [[nodiscard]] static DeleteBuilder deleteFrom(const Table& table) {
        return DeleteBuilder(table);
    }

    /**
     * @brief Create a table object
     * @param name The table name
     * @return A Table object
     */
    [[nodiscard]] static Table table(std::string_view name) {
        return Table(name);
    }

    /**
     * @brief Create a column object
     * @param name The column name
     * @return A Column object
     */
    [[nodiscard]] static Column column(std::string_view name) {
        return Column(name);
    }

    /**
     * @brief Create a column object for a table
     * @param name The column name
     * @param table The table the column belongs to
     * @return A Column object
     */
    [[nodiscard]] static Column column(std::string_view name, const Table& table) {
        return Column(name, table);
    }

    /**
     * @brief Create a row object
     * @return A Row object
     */
    [[nodiscard]] static Row row() {
        return Row();
    }

    /**
     * @brief Create a row object with values
     * @param values The column values
     * @return A Row object
     */
    [[nodiscard]] static Row row(const std::unordered_map<std::string, Variant>& values) {
        return Row(values);
    }

    /**
     * @brief Create a column-order pair for use with orderBy
     * @param column The column to order by
     * @param order The sort order
     * @return A pair of column and sort order
     */
    [[nodiscard]] static std::pair<Column, SqlSortOrder> order(const Column& column, SqlSortOrder order = SqlSortOrder::Ascending) {
        return std::make_pair(column, order);
    }

    /**
     * @brief Create a column-value pair for use with set methods
     * @param column The column
     * @param value The value
     * @return A pair of column and value
     */
    [[nodiscard]] static std::pair<Column, Variant> value(const Column& column, const Variant& value) {
        return std::make_pair(column, value);
    }

    /**
     * @brief Create a tuple of columns for use with insert method
     * @tparam Args Types of the columns (must be convertible to Column)
     * @param columns The columns
     * @return A tuple of columns
     */
    template<ColumnConvertible... Args>
    [[nodiscard]] static auto columns(Args&&... columns) {
        return std::make_tuple(Column(std::forward<Args>(columns))...);
    }

    /**
     * @brief Create a tuple of values for use with insert method
     * @tparam Args Types of the values (must be convertible to Variant)
     * @param values The values
     * @return A tuple of values
     */
    template<VariantConvertible... Args>
    [[nodiscard]] static auto values(Args&&... values) {
        return std::make_tuple(Variant(std::forward<Args>(values))...);
    }
};

} // namespace database

#endif // DATABASE_QUERY_BUILDER_H
