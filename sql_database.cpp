#include "sql_database.h"

#include <vector>
#include <algorithm>
#include <iostream>
#include <memory>
#include <string>
#include <string_view>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>
#include <functional>
#include <format>

namespace database {

// Forward declarations
class SqlDriverCreatorBase;

// Default connection name
const char* defaultConnectionName = "default";

/**
 * @brief Private implementation class for SqlDatabase
 *
 * This class provides the implementation details for SqlDatabase.
 * It manages the database connection and provides static methods
 * for managing database connections and drivers.
 */
class SqlDatabasePrivate {
public:
    SqlDatabasePrivate();
    SqlDatabasePrivate(std::unique_ptr<SqlDriver> driver, const ConnectionParams& params);
    ~SqlDatabasePrivate();

    // Delete copy constructor and assignment operator
    SqlDatabasePrivate(const SqlDatabasePrivate&) = delete;
    SqlDatabasePrivate& operator=(const SqlDatabasePrivate&) = delete;

    // Connection details
    std::unique_ptr<SqlDriver> driver;
    ConnectionParams params;
    std::string connectionName;
    bool connected;
    bool transactionActive;
    mutable SqlError lastError;

    // Initialize with driver type
    void init(std::string_view type);

    // Static methods for database management
    [[nodiscard]] static SqlDatabase database(std::string_view name, bool open);
    static void addDatabase(SqlDatabase& db, std::string_view name);
    static void removeDatabase(std::string_view name);
    static void invalidateDb(SqlDatabase& db, std::string_view name, bool doWarn = true);

    // Global registry for database connections and drivers
    class Registry {
    public:
        // Singleton access
        static Registry& instance();

        // Driver registration and lookup
        void registerDriver(std::string_view name, std::shared_ptr<SqlDriverCreatorBase> creator);
        [[nodiscard]] std::unique_ptr<SqlDriver> createDriver(std::string_view name);
        [[nodiscard]] bool hasDriver(std::string_view name) const;
        [[nodiscard]] std::vector<std::string> driverNames() const;

        // Connection management
        void addConnection(std::string_view name, SqlDatabase& db);
        [[nodiscard]] SqlDatabase getConnection(std::string_view name);
        void removeConnection(std::string_view name);
        [[nodiscard]] bool hasConnection(std::string_view name) const;
        [[nodiscard]] std::vector<std::string> connectionNames() const;

    private:
        Registry() = default;
        ~Registry() = default;

        // Delete copy constructor and assignment operator
        Registry(const Registry&) = delete;
        Registry& operator=(const Registry&) = delete;

        // Thread-safe access to registry data
        mutable std::shared_mutex mutex;
        std::unordered_map<std::string, std::shared_ptr<SqlDriverCreatorBase>> registeredDrivers;
        std::unordered_map<std::string, std::weak_ptr<SqlDatabasePrivate>, StringViewHash, std::equal_to<>> connections;
    };
};

// Implementation of SqlDatabasePrivate

SqlDatabasePrivate::SqlDatabasePrivate()
    : driver(nullptr),
    connected(false),
    transactionActive(false) {
}

SqlDatabasePrivate::SqlDatabasePrivate(std::unique_ptr<SqlDriver> driver, const ConnectionParams& params)
    : driver(std::move(driver)),
    params(params),
    connected(false),
    transactionActive(false) {
}

SqlDatabasePrivate::~SqlDatabasePrivate() = default;

void SqlDatabasePrivate::init(std::string_view type) {
    // Create a driver for the specified type
    auto& registry = Registry::instance();

    // Convert type to lowercase for case-insensitive comparison
    std::string lowerType;
    lowerType.reserve(type.size());
    std::transform(type.begin(), type.end(), std::back_inserter(lowerType), ::tolower);

    // Create the driver
    driver = registry.createDriver(lowerType);
    if (!driver) {
        std::cerr << "SqlDatabase: No such driver: " << type << std::endl;
    }
}

SqlDatabase SqlDatabasePrivate::database(std::string_view name, bool open) {
    auto& registry = Registry::instance();
    auto db = registry.getConnection(name);

    if (db.isValid() && open && !db.isConnected()) {
        db.connect();
    }

    return db;
}

void SqlDatabasePrivate::addDatabase(SqlDatabase& db, std::string_view name) {
    auto& registry = Registry::instance();

    // Check if the connection already exists
    if (registry.hasConnection(name)) {
        // Get the existing connection and invalidate it
        auto existingDb = registry.getConnection(name);
        invalidateDb(existingDb, name);
    }

    // Add the new connection
    registry.addConnection(name, db);
    db.d_ptr->connectionName = std::string(name);
}

void SqlDatabasePrivate::removeDatabase(std::string_view name) {
    auto& registry = Registry::instance();

    // Check if the connection exists
    if (registry.hasConnection(name)) {
        // Get the existing connection and invalidate it
        auto db = registry.getConnection(name);
        invalidateDb(db, name);

        // Remove the connection
        registry.removeConnection(name);
    }
}

void SqlDatabasePrivate::invalidateDb(SqlDatabase& db, std::string_view name, bool doWarn) {
    if (doWarn) {
        std::cerr << "SqlDatabase: Connection " << name << " is still in use, all queries will cease to work." << std::endl;
    }

    // Close the connection
    if (db.isConnected()) {
        db.disconnect();
    }
}

// Registry implementation

SqlDatabasePrivate::Registry& SqlDatabasePrivate::Registry::instance() {
    static Registry registry;
    return registry;
}

void SqlDatabasePrivate::Registry::registerDriver(std::string_view name, std::shared_ptr<SqlDriverCreatorBase> creator) {
    if (!creator) {
        return;
    }

    // Convert name to lowercase for case-insensitive comparison
    std::string lowerName;
    lowerName.reserve(name.size());
    std::transform(name.begin(), name.end(), std::back_inserter(lowerName), ::tolower);

    // Register the driver
    std::unique_lock<std::shared_mutex> lock(mutex);
    registeredDrivers[lowerName] = std::move(creator);
}

std::unique_ptr<SqlDriver> SqlDatabasePrivate::Registry::createDriver(std::string_view name) {
    // Convert name to lowercase for case-insensitive comparison
    std::string lowerName;
    lowerName.reserve(name.size());
    std::transform(name.begin(), name.end(), std::back_inserter(lowerName), ::tolower);

    // Look up the driver creator
    std::shared_lock<std::shared_mutex> lock(mutex);
    auto it = registeredDrivers.find(lowerName);
    if (it != registeredDrivers.end()) {
        return it->second->createDriver();
    }

    return nullptr;
}

bool SqlDatabasePrivate::Registry::hasDriver(std::string_view name) const {
    // Convert name to lowercase for case-insensitive comparison
    std::string lowerName;
    lowerName.reserve(name.size());
    std::transform(name.begin(), name.end(), std::back_inserter(lowerName), ::tolower);

    // Check if the driver exists
    std::shared_lock<std::shared_mutex> lock(mutex);
    return registeredDrivers.find(lowerName) != registeredDrivers.end();
}

std::vector<std::string> SqlDatabasePrivate::Registry::driverNames() const {
    std::shared_lock<std::shared_mutex> lock(mutex);
    std::vector<std::string> result;
    result.reserve(registeredDrivers.size());

    for (const auto& entry : registeredDrivers) {
        result.push_back(entry.first);
    }

    return result;
}

void SqlDatabasePrivate::Registry::addConnection(std::string_view name, SqlDatabase& db) {
    std::unique_lock<std::shared_mutex> lock(mutex);
    connections[std::string(name)] = std::weak_ptr<SqlDatabasePrivate>(db.d_ptr);
}

SqlDatabase SqlDatabasePrivate::Registry::getConnection(std::string_view name) {
    std::shared_lock<std::shared_mutex> lock(mutex);
    auto it = connections.find(name);
    if (it != connections.end()) {
        if (auto dbPrivate = it->second.lock()) {
            // Create a new SqlDatabase with the existing private implementation
            SqlDatabase db;
            db.d_ptr = std::move(dbPrivate);
            return db;
        }
    }

    // Return an invalid database
    return SqlDatabase();
}

void SqlDatabasePrivate::Registry::removeConnection(std::string_view name) {
    std::unique_lock<std::shared_mutex> lock(mutex);
    connections.erase(std::string(name));
}

bool SqlDatabasePrivate::Registry::hasConnection(std::string_view name) const {
    std::shared_lock<std::shared_mutex> lock(mutex);

    if (auto it = connections.find(name); it != connections.end()) {
        return !it->second.expired();
    }
    return false;
}

std::vector<std::string> SqlDatabasePrivate::Registry::connectionNames() const {
    std::shared_lock<std::shared_mutex> lock(mutex);
    std::vector<std::string> result;
    result.reserve(connections.size());

    for (const auto& [name, weakPtr] : connections) {
        if (!weakPtr.expired()) {
            result.push_back(name);
        }
    }

    return result;
}

// Implementation of SqlDatabase

SqlDatabase::SqlDatabase()
    : d_ptr(std::make_shared<SqlDatabasePrivate>()) {
}

SqlDatabase::SqlDatabase(std::unique_ptr<SqlDriver> driver, const ConnectionParams& params)
    : d_ptr(std::make_shared<SqlDatabasePrivate>(std::move(driver), params)) {
    if (!d_ptr->driver) {
        throw SqlException("Invalid database driver", ErrorCode::InvalidArgument);
    }
}

SqlDatabase::~SqlDatabase() {
    if (d_ptr->connected) {
        try {
            disconnect();
        } catch (const std::exception& e) {
            // Log but don't throw from destructor
            std::cerr << "Error during disconnect in SqlDatabase destructor: " << e.what() << std::endl;
        }
    }
}

SqlDatabase::SqlDatabase(const SqlDatabase& other)
    : d_ptr(other.d_ptr) {
}

SqlDatabase& SqlDatabase::operator=(const SqlDatabase& other) {
    if (this != &other) {
        d_ptr = other.d_ptr;
    }
    return *this;
}

SqlDatabase::SqlDatabase(SqlDatabase&& other) noexcept
    : d_ptr(std::move(other.d_ptr)) {
    other.d_ptr = std::make_shared<SqlDatabasePrivate>();
}

SqlDatabase& SqlDatabase::operator=(SqlDatabase&& other) noexcept {
    if (this != &other) {
        d_ptr = std::move(other.d_ptr);
        other.d_ptr = std::make_shared<SqlDatabasePrivate>();
    }
    return *this;
}

SqlDatabase::SqlDatabase(std::string_view type)
    : d_ptr(std::make_shared<SqlDatabasePrivate>()) {
    d_ptr->init(type);
}

bool SqlDatabase::connect() {
    // Check if already connected
    if (d_ptr->connected) {
        return true;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        std::string category(getErrorCategory(ErrorCode::InvalidArgument));
        std::string errorCode(errorCodeToString(ErrorCode::InvalidArgument));
        std::string errorMessage = std::format("Invalid database driver [{}: {}]", category, errorCode);
        d_ptr->lastError = SqlError(errorMessage, ErrorCode::InvalidArgument);
        return false;
    }

    // Connect to the database
    bool result = d_ptr->driver->connect(d_ptr->params);
    if (result) {
        d_ptr->connected = true;
        d_ptr->lastError.clear();
    } else {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

bool SqlDatabase::disconnect() {
    // Check if already disconnected
    if (!d_ptr->connected) {
        return true;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        std::string category(getErrorCategory(ErrorCode::InvalidArgument));
        std::string errorCode(errorCodeToString(ErrorCode::InvalidArgument));
        std::string errorMessage = std::format("Invalid database driver [{}: {}]", category, errorCode);
        d_ptr->lastError = SqlError(errorMessage, ErrorCode::InvalidArgument);
        return false;
    }

    // Rollback any active transaction
    if (d_ptr->transactionActive) {
        try {
            rollback();
        } catch (const std::exception& e) {
            // Log the error but continue with disconnect
            std::cerr << "Error rolling back transaction during disconnect: " << e.what() << std::endl;
        }
    }

    // Disconnect from the database
    bool result = d_ptr->driver->disconnect();
    if (result) {
        d_ptr->connected = false;
    } else {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

bool SqlDatabase::isConnected() const noexcept {
    // Check if driver is valid
    if (!d_ptr->driver) {
        return false;
    }

    return d_ptr->connected && d_ptr->driver->isConnected();
}

std::shared_ptr<SqlStatement> SqlDatabase::createStatement() {
    // Check if connected
    if (!d_ptr->connected) {
        std::string category(getErrorCategory(ErrorCode::ConnectionClosed));
        std::string errorCode(errorCodeToString(ErrorCode::ConnectionClosed));
        std::string errorMessage = std::format("Not connected to database [{}: {}]", category, errorCode);
        d_ptr->lastError = SqlError(errorMessage, ErrorCode::ConnectionClosed);
        return nullptr;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return nullptr;
    }

    // Create a statement
    auto statement = d_ptr->driver->createStatement();
    if (!statement) {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return statement;
}

std::shared_ptr<SqlStatement> SqlDatabase::prepareStatement(std::string_view sql) {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return nullptr;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return nullptr;
    }

    // Prepare a statement
    auto statement = d_ptr->driver->createStatement();
    if (!statement) {
        d_ptr->lastError = d_ptr->driver->lastError();
        return nullptr;
    }
    if (!statement->prepare(sql)) {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return statement;
}

bool SqlDatabase::transaction(TransactionIsolation level) {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if transaction is already active
    if (d_ptr->transactionActive) {
        std::string category(getErrorCategory(ErrorCode::TransactionAlreadyActive));
        std::string errorCode(errorCodeToString(ErrorCode::TransactionAlreadyActive));
        std::string errorMessage = std::format("Transaction already active [{}: {}]", category, errorCode);
        d_ptr->lastError = SqlError(errorMessage, ErrorCode::TransactionAlreadyActive);
        return false;
    }

    // Begin a transaction
    bool result = d_ptr->driver->beginTransaction(level);
    if (result) {
        d_ptr->transactionActive = true;
        d_ptr->lastError.clear();
    } else {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

bool SqlDatabase::commit() {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if transaction is active
    if (!d_ptr->transactionActive) {
        std::string category(getErrorCategory(ErrorCode::TransactionNotActive));
        std::string errorCode(errorCodeToString(ErrorCode::TransactionNotActive));
        std::string errorMessage = std::format("No active transaction [{}: {}]", category, errorCode);
        d_ptr->lastError = SqlError(errorMessage, ErrorCode::TransactionNotActive);
        return false;
    }

    // Commit the transaction
    bool result = d_ptr->driver->commitTransaction();
    if (result) {
        d_ptr->transactionActive = false;
        d_ptr->lastError.clear();
    } else {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

bool SqlDatabase::rollback() {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if transaction is active
    if (!d_ptr->transactionActive) {
        d_ptr->lastError = SqlError("No active transaction", ErrorCode::TransactionNotActive);
        return false;
    }

    // Rollback the transaction
    bool result = d_ptr->driver->rollbackTransaction();
    if (result) {
        d_ptr->transactionActive = false;
        d_ptr->lastError.clear();
    } else {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

bool SqlDatabase::isTransactionActive() const noexcept {
    return d_ptr->transactionActive;
}

bool SqlDatabase::createSavepoint(std::string_view name) {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if transaction is active
    if (!d_ptr->transactionActive) {
        d_ptr->lastError = SqlError("No active transaction", ErrorCode::TransactionNotActive);
        return false;
    }

    // Create a savepoint
    bool result = d_ptr->driver->createSavepoint(name);
    if (!result) {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

bool SqlDatabase::rollbackToSavepoint(std::string_view name) {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if transaction is active
    if (!d_ptr->transactionActive) {
        d_ptr->lastError = SqlError("No active transaction", ErrorCode::TransactionNotActive);
        return false;
    }

    // Rollback to a savepoint
    bool result = d_ptr->driver->rollbackToSavepoint(name);
    if (!result) {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

bool SqlDatabase::releaseSavepoint(std::string_view name) {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if transaction is active
    if (!d_ptr->transactionActive) {
        d_ptr->lastError = SqlError("No active transaction", ErrorCode::TransactionNotActive);
        return false;
    }

    // Release a savepoint
    bool result = d_ptr->driver->releaseSavepoint(name);
    if (!result) {
        d_ptr->lastError = d_ptr->driver->lastError();
    }

    return result;
}

const SqlError& SqlDatabase::lastError() const noexcept {
    return d_ptr->lastError;
}

ConnectionMetadata SqlDatabase::metadata() const {
    // Check if connected
    if (!d_ptr->connected) {
        d_ptr->lastError = SqlError("Not connected to database", ErrorCode::ConnectionClosed);
        return ConnectionMetadata();
    }

    // Check if driver is valid
    if (!d_ptr->driver) {
        d_ptr->lastError = SqlError("Invalid database driver", ErrorCode::InvalidArgument);
        return ConnectionMetadata();
    }

    // Get metadata
    return d_ptr->driver->metadata();
}

// Accessor methods

SqlDriver* SqlDatabase::driver() const noexcept {
    return d_ptr->driver.get();
}

const ConnectionParams& SqlDatabase::connectionParams() const noexcept {
    return d_ptr->params;
}

bool SqlDatabase::isValid() const noexcept {
    return d_ptr->driver != nullptr;
}

std::string_view SqlDatabase::driverName() const {
    if (d_ptr->driver) {
        return d_ptr->driver->name();
    }
    return "";
}

std::string_view SqlDatabase::databaseName() const {
    return d_ptr->params.database();
}

std::string_view SqlDatabase::userName() const {
    return d_ptr->params.userName();
}

std::string_view SqlDatabase::password() const {
    return d_ptr->params.password();
}

std::string_view SqlDatabase::hostName() const {
    return d_ptr->params.hostName();
}

int SqlDatabase::port() const noexcept {
    return d_ptr->params.port();
}

std::string SqlDatabase::connectOptions() const {
    // Return a string representation of the options
    std::string result;
    for (const auto& option : d_ptr->params.options()) {
        if (!result.empty()) {
            result += ";";
        }
        result += option.first + "=" + option.second;
    }
    return result;
}

std::string_view SqlDatabase::connectionName() const {
    return d_ptr->connectionName;
}

// Setter methods

void SqlDatabase::setDatabaseName(std::string_view name) {
    d_ptr->params.setDatabase(name);
}

void SqlDatabase::setUserName(std::string_view name) {
    d_ptr->params.setUserName(name);
}

void SqlDatabase::setPassword(std::string_view password) {
    d_ptr->params.setPassword(password);
}

void SqlDatabase::setHostName(std::string_view host) {
    d_ptr->params.setHost(host);
}

void SqlDatabase::setPort(int port) noexcept {
    d_ptr->params.setPort(port);
}

void SqlDatabase::setConnectOptions(std::string_view options) {
    // Parse options string (format: "option1=value1;option2=value2")
    if (options.empty()) {
        return;
    }

    std::string_view option;
    std::string_view value;
    std::string_view::size_type pos = 0;
    std::string_view::size_type end;

    while (pos < options.length()) {
        // Find the end of the option=value pair
        end = options.find(';', pos);
        if (end == std::string_view::npos) {
            end = options.length();
        }

        // Extract the option=value pair
        std::string_view pair = options.substr(pos, end - pos);

        // Find the equals sign
        std::string_view::size_type equals = pair.find('=');
        if (equals != std::string_view::npos) {
            // Extract the option and value
            option = pair.substr(0, equals);
            value = pair.substr(equals + 1);

            // Set the option
            d_ptr->params.setOption(std::string(option), std::string(value));
        }

        // Move to the next option=value pair
        pos = end + 1;
    }
}

// Static methods

SqlDatabase SqlDatabase::addDatabase(std::string_view type, std::string_view connectionName) {
    SqlDatabase db(type);
    SqlDatabasePrivate::addDatabase(db, connectionName);
    return db;
}

SqlDatabase SqlDatabase::addDatabase(std::unique_ptr<SqlDriver> driver, std::string_view connectionName) {
    SqlDatabase db(std::move(driver), ConnectionParams());
    SqlDatabasePrivate::addDatabase(db, connectionName);
    return db;
}

SqlDatabase SqlDatabase::cloneDatabase(const SqlDatabase& other, std::string_view connectionName) {
    SqlDatabase db;
    if (other.d_ptr->driver) {
        // Create a new driver of the same type
        auto& registry = SqlDatabasePrivate::Registry::instance();
        auto newDriver = registry.createDriver(other.d_ptr->driver->name());
        if (newDriver) {
            db = SqlDatabase(std::move(newDriver), other.d_ptr->params);
            SqlDatabasePrivate::addDatabase(db, connectionName);
        }
    }
    return db;
}

SqlDatabase SqlDatabase::database(std::string_view connectionName, bool open) {
    return SqlDatabasePrivate::database(connectionName, open);
}

void SqlDatabase::removeDatabase(std::string_view connectionName) {
    SqlDatabasePrivate::removeDatabase(connectionName);
}

bool SqlDatabase::contains(std::string_view connectionName) {
    auto& registry = SqlDatabasePrivate::Registry::instance();
    return registry.hasConnection(connectionName);
}

std::vector<std::string> SqlDatabase::drivers() {
    auto& registry = SqlDatabasePrivate::Registry::instance();
    return registry.driverNames();
}

std::vector<std::string> SqlDatabase::connectionNames() {
    auto& registry = SqlDatabasePrivate::Registry::instance();
    return registry.connectionNames();
}

void SqlDatabase::registerSqlDriver(std::string_view name, std::shared_ptr<SqlDriverCreatorBase> creator) {
    if (!creator) {
        return;
    }

    auto& registry = SqlDatabasePrivate::Registry::instance();
    registry.registerDriver(name, std::move(creator));
}

void SqlDatabase::registerSqlDriver(std::string_view name, std::function<std::unique_ptr<SqlDriver>()> factory) {
    if (!factory) {
        return;
    }

    class FunctionDriverCreator : public SqlDriverCreatorBase {
    public:
        explicit FunctionDriverCreator(std::function<std::unique_ptr<SqlDriver>()> f)
            : factory(std::move(f)) {}

        [[nodiscard]] std::unique_ptr<SqlDriver> createDriver() const override {
            return factory();
        }

    private:
        std::function<std::unique_ptr<SqlDriver>()> factory;
    };

    registerSqlDriver(name, std::make_shared<FunctionDriverCreator>(std::move(factory)));
}

bool SqlDatabase::isDriverAvailable(std::string_view name) {
    auto& registry = SqlDatabasePrivate::Registry::instance();
    return registry.hasDriver(name);
}

} // namespace database
