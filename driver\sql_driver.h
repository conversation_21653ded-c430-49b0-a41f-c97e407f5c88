#ifndef DATABASE_SQL_DRIVER_H
#define DATABASE_SQL_DRIVER_H

#include <memory>
#include <string_view>
#include <vector>

#include "connection/connection_params.h"
#include "exception/sql_error.h"
#include "builder/sql_table.h"
#include "builder/sql_index.h"

namespace database {

// Forward declarations
class SqlStatement;
class SqlDatabase;

/**
 * @brief Enumeration of database driver features
 */
enum class DriverFeature {
    Transactions,
    BatchOperations,
    PreparedStatements,
    NamedParameters,
    StoredProcedures,
    Schemas,
    MultipleResultSets,
    GeneratedKeys,
    CursorSupport,
    TransactionIsolation,
    SavePoints
};

/**
 * @brief Transaction isolation levels
 */
enum class TransactionIsolation {
    ReadUncommitted,
    ReadCommitted,
    RepeatableRead,
    Serializable
};

/**
 * @brief Interface for database drivers
 *
 * This class defines the entry point for database drivers.
 * Each driver must implement this interface to provide access to
 * database-specific functionality.
 */
class SqlDriver {
public:
    /**
     * @brief Connection validation result
     */
    enum class ValidationResult {
        Valid,              ///< Connection is valid and ready to use
        Invalid,            ///< Connection is invalid and should be discarded
        Recoverable         ///< Connection is invalid but can be recovered
    };

    virtual ~SqlDriver() = default;

    /**
     * @brief Connect to the database
     * @param params Connection parameters
     * @return True if successful, false otherwise
     */
    virtual bool connect(const ConnectionParams& params) = 0;

    /**
     * @brief Disconnect from the database
     * @return True if successful, false otherwise
     */
    virtual bool disconnect() = 0;

    /**
     * @brief Check if connected to the database
     * @return True if connected, false otherwise
     */
    [[nodiscard]] virtual bool isConnected() const = 0;

    /**
     * @brief Validate a database connection
     * @param query The validation query (optional)
     * @return The validation result
     */
    virtual ValidationResult validateConnection(std::string_view query = "") = 0;

    /**
     * @brief Create a statement for executing SQL
     * @return A shared pointer to a statement
     */
    [[nodiscard]] virtual std::shared_ptr<SqlStatement> createStatement() = 0;

    /**
     * @brief Begin a transaction
     * @param level The transaction isolation level
     * @return True if successful, false otherwise
     */
    virtual bool beginTransaction(TransactionIsolation level = TransactionIsolation::ReadCommitted) = 0;

    /**
     * @brief Commit the current transaction
     * @return True if successful, false otherwise
     */
    virtual bool commitTransaction() = 0;

    /**
     * @brief Rollback the current transaction
     * @return True if successful, false otherwise
     */
    virtual bool rollbackTransaction() = 0;

    /**
     * @brief Create a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    virtual bool createSavepoint(std::string_view name) = 0;

    /**
     * @brief Rollback to a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    virtual bool rollbackToSavepoint(std::string_view name) = 0;

    /**
     * @brief Release a savepoint
     * @param name The savepoint name
     * @return True if successful, false otherwise
     */
    virtual bool releaseSavepoint(std::string_view name) = 0;

    /**
     * @brief Get connection metadata
     * @return The connection metadata
     */
    [[nodiscard]] virtual ConnectionMetadata metadata() const = 0;

    /**
     * @brief Get the driver name
     * @return The driver name
     */
    [[nodiscard]] virtual std::string_view name() const = 0;

    /**
     * @brief Get the driver version
     * @return The driver version
     */
    [[nodiscard]] virtual std::string_view version() const = 0;

    /**
     * @brief Ping the database to check if the connection is still alive
     * @return True if the connection is alive, false otherwise
     */
    [[nodiscard]] virtual bool ping() = 0;

    /**
     * @brief Get the last error message
     * @return The error message
     */
    [[nodiscard]] virtual const SqlError& lastError() const = 0;

    /**
     * @brief Check if a feature is supported
     * @param feature The feature to check
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsFeature(DriverFeature feature) const = 0;

    /**
     * @brief Get the list of supported features
     * @return The list of supported features
     */
    [[nodiscard]] virtual std::vector<DriverFeature> getSupportedFeatures() const = 0;

    // Database Object API (using core classes)

    /**
     * @brief Get all tables in the database
     * @param schema The schema name (optional, empty for all schemas)
     * @param type The object type filter (optional)
     * @return A vector of SqlTable objects with driver set for lazy loading
     */
    [[nodiscard]] virtual std::vector<SqlTable> getTables(
        std::string_view schema = "",
        SqlObjectType type = SqlObjectType::Table) const = 0;

    /**
     * @brief Check if a table exists
     * @param table The table to check
     * @return True if the table exists, false otherwise
     */
    [[nodiscard]] virtual bool tableExists(const SqlTable& table) const = 0;

    /**
     * @brief Check if an index exists
     * @param index The index to check
     * @return True if the index exists, false otherwise
     */
    [[nodiscard]] virtual bool indexExists(const SqlIndex& index) const = 0;

    /**
     * @brief Get all schema names in the database
     * @return A vector of schema names
     */
    [[nodiscard]] virtual std::vector<std::string> getSchemas() const = 0;

    /**
     * @brief Check if a schema exists
     * @param schemaName The schema name to check
     * @return True if the schema exists, false otherwise
     */
    [[nodiscard]] virtual bool schemaExists(std::string_view schemaName) const = 0;

    /**
     * @brief Create a SqlTable object for the given table name
     * @param tableName The table name
     * @param schema The schema name (optional)
     * @return A SqlTable object with driver set for lazy loading
     */
    [[nodiscard]] SqlTable table(std::string_view tableName, std::string_view schema = "") const {
        SqlTable t(tableName, const_cast<SqlDriver*>(this));
        if (!schema.empty()) {
            t.setSchema(schema);
        }
        return t;
    }

    /**
     * @brief Create a SqlIndex object for the given index name
     * @param indexName The index name
     * @param tableName The table name (optional)
     * @param schema The schema name (optional)
     * @return A SqlIndex object with driver set for lazy loading
     */
    [[nodiscard]] SqlIndex index(std::string_view indexName, 
                                 std::string_view tableName = "", 
                                 std::string_view schema = "") const {
        SqlIndex idx(indexName, nullptr, const_cast<SqlDriver*>(this));
        if (!tableName.empty()) {
            idx.setTableName(tableName);
        }
        if (!schema.empty()) {
            idx.setSchema(schema);
        }
        return idx;
    }

    // Internal Metadata API (for advanced use cases and internal implementation)

    /**
     * @brief Get table metadata from the database
     * @param table The table to get metadata for
     * @return Table metadata structure
     * @throws SqlException if the operation fails
     */
    [[nodiscard]] virtual SqlTable::Metadata getTableMetadata(const SqlTable& table) const = 0;

    /**
     * @brief Get index metadata from the database
     * @param index The index to get metadata for
     * @return Index metadata structure
     * @throws SqlException if the operation fails
     */
    [[nodiscard]] virtual SqlIndex::Metadata getIndexMetadata(const SqlIndex& index) const = 0;

    /**
     * @brief Get the maximum number of connections supported by the driver
     * @return The maximum number of connections, or 0 if unlimited
     */
    [[nodiscard]] virtual int getMaxConnections() const { return 0; }

    /**
     * @brief Get the default transaction isolation level
     * @return The default transaction isolation level
     */
    [[nodiscard]] virtual TransactionIsolation getDefaultTransactionIsolation() const {
        return TransactionIsolation::ReadCommitted;
    }

    /**
     * @brief Check if the driver supports multiple open connections
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsMultipleConnections() const { return true; }

    /**
     * @brief Check if the driver supports connection pooling
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsConnectionPooling() const { return true; }

    /**
     * @brief Check if the driver supports connection validation
     * @return True if supported, false otherwise
     */
    [[nodiscard]] virtual bool supportsConnectionValidation() const { return true; }

    /**
     * @brief Get the default validation query
     * @return The default validation query
     */
    [[nodiscard]] virtual std::string_view getDefaultValidationQuery() const { return "SELECT 1"; }

    /**
     * @brief Check if the driver is thread-safe
     * @return True if thread-safe, false otherwise
     */
    [[nodiscard]] virtual bool isThreadSafe() const { return true; }
};

} // namespace database

#endif // DATABASE_SQL_DRIVER_H 