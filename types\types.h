#ifndef TYPES_H
#define TYPES_H

#include <cstddef>
#include <type_traits>

template <int> struct IntegerForSize;
template <>    struct IntegerForSize<1> { typedef unsigned char Unsigned; typedef signed char Signed; };
template <>    struct IntegerForSize<2> { typedef unsigned short Unsigned; typedef short Signed; };
template <>    struct IntegerForSize<4> { typedef unsigned int Unsigned; typedef int Signed; };
template <>    struct IntegerForSize<8> { typedef unsigned long long Unsigned; typedef long long Signed; };

template <class T> struct IntegerForSizeof: IntegerForSize<sizeof(T)> { };
// typedef IntegerForSize<PROCESSOR_WORDSIZE>::Signed qregisterint;
// typedef IntegerForSize<PROCESSOR_WORDSIZE>::Unsigned qregisteruint;
// typedef IntegerForSizeof<void *>::Unsigned quintptr;
// typedef IntegerForSizeof<void *>::Signed qptrdiff;
// typedef qptrdiff qintptr;
using sizetype = IntegerForSizeof<std::size_t>::Signed;

// qtypeinfo.h
template <typename T>
inline constexpr bool isRelocatable =  std::is_trivially_copyable_v<T> && std::is_trivially_destructible_v<T>;
template <typename T>
inline constexpr bool isValueInitializationBitwiseZero =
    std::is_scalar_v<T> && !std::is_member_object_pointer_v<T>;

template <typename T>
class TypeInfo
{
public:
    enum {
        isPointer [[deprecated("Use std::is_pointer instead")]] = std::is_pointer_v<T>,
        isIntegral [[deprecated("Use std::is_integral instead")]] = std::is_integral_v<T>,
        isComplex = !std::is_trivial_v<T>,
        isRelocatable = isRelocatable<T>,
        isValueInitializationBitwiseZero = isValueInitializationBitwiseZero<T>,
    };
};

#endif // TYPES_H
