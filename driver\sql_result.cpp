#include "sql_result.h"
#include "sql_statement.h"

#include <algorithm>
#include <utility>

namespace database {

// Initial cache size (similar to QSqlCachedResult)
static constexpr int INITIAL_CACHE_SIZE = 128;
// Maximum cache size to prevent excessive memory usage
static constexpr int MAX_CACHE_SIZE = 10000;

/**
 * @brief Private implementation class for SqlResult
 *
 * This class provides the implementation details for result set caching
 * and navigation. It supports both forward-only and scrollable result sets.
 */
class SqlResultPrivate {
public:
    /**
     * @brief Constructor
     * @param q Pointer to the public class
     * @param forwardOnly Whether the result set is forward-only
     */
    SqlResultPrivate(SqlResult* q, bool forwardOnly = false)
        : forwardOnly(forwardOnly)
        , q_ptr(q)
    {}

    /**
     * @brief Virtual destructor
     */
    virtual ~SqlResultPrivate() = default;

    /**
     * @brief Initialize the result set
     * @param count The number of columns
     */
    void init(int count) {
        cleanup();
        columnCount = count;

        // Clear and shrink to reclaim memory
        cache.clear();
        cache.shrink_to_fit();

        // Pre-allocate with appropriate capacity to avoid reallocations
        cache.reserve(forwardOnly ? 1 : INITIAL_CACHE_SIZE);
    }

    /**
     * @brief Clean up the result set
     */
    void cleanup() {
        cache.clear();
        cache.shrink_to_fit();
        atEnd = false;
        columnCount = 0;
    }

    /**
     * @brief Check if a row can be accessed
     * @param row The row number (0-based)
     * @return True if the row can be accessed, false otherwise
     */
    [[nodiscard]] bool canSeek(int row) const {
        if (forwardOnly || row < 0) {
            return false;
        }

        return row < static_cast<int>(cache.size());
    }

    /**
     * @brief Get the number of rows in the cache
     * @return The number of rows
     */
    [[nodiscard]] int cacheCount() const {
        return static_cast<int>(cache.size());
    }

    /**
     * @brief Add a new record to the cache
     * @param record The record to add
     */
    void addRecord(const SqlRecord& record) {
        if (forwardOnly) {
            // For forward-only result sets, we replace the single cached row
            if (cache.empty()) {
                cache.push_back(record);
            } else {
                cache[0] = record;
            }
            return;
        }

        // For scrollable result sets, manage cache size if needed
        manageScrollableCacheSize();

        // Add the new record
        cache.push_back(record);
    }

    /**
     * @brief Add a new record to the cache (move semantics)
     * @param record The record to move
     */
    void addRecord(SqlRecord&& record) {
        if (forwardOnly) {
            // For forward-only result sets, we replace the single cached row
            if (cache.empty()) {
                cache.push_back(std::move(record));
            } else {
                cache[0] = std::move(record);
            }
            return;
        }

        // For scrollable result sets, manage cache size if needed
        manageScrollableCacheSize();

        // Add the new record using move semantics
        cache.push_back(std::move(record));
    }

    /**
     * @brief Revert the last fetch (remove the last record)
     */
    void revertLast() {
        if (forwardOnly || cache.empty()) {
            return;
        }

        cache.pop_back();
    }

private:
    /**
     * @brief Manage the cache size for scrollable result sets
     *
     * This method ensures the cache doesn't exceed the maximum size
     * by removing older records when necessary.
     */
    void manageScrollableCacheSize() {
        // Check if we've reached the maximum cache size
        if (cache.size() >= MAX_CACHE_SIZE) {
            // If we're at max capacity, remove the oldest records
            // Keep the most recent half of the records
            size_t keepCount = MAX_CACHE_SIZE / 2;
            size_t removeCount = cache.size() - keepCount;

            // Move elements to the beginning of the vector
            std::move(cache.begin() + removeCount, cache.end(), cache.begin());
            cache.resize(keepCount);

            // Update the atEnd flag since we're no longer caching all records
            atEnd = false;
        }
    }

public:
    // Cache data using SqlRecord
    std::vector<SqlRecord> cache;
    int columnCount = 0;
    bool atEnd = false;
    bool forwardOnly = false;

    // Pointer to the public class
    SqlResult* q_ptr;
};

SqlResult::SqlResult(bool forwardOnly)
    : d_ptr(std::make_unique<SqlResultPrivate>(this, forwardOnly))
    , m_initialized(false)
{
}

SqlResult::~SqlResult() {
    // Close the result set to ensure proper cleanup
    close();
}

void SqlResult::initialize(SqlStatement* statement, const SqlRecord& metadata,
                           const std::vector<std::string>& columnNames) {
    if (!statement) {
        m_lastError = SqlError("Invalid statement", ErrorCode::InvalidArgument);
        return;
    }

    // Store the statement
    m_statement = statement;

    // Initialize column information
    int columnCount = static_cast<int>(metadata.size());
    init(columnCount);

    // Set column names
    if (!columnNames.empty()) {
        setColumnNames(columnNames);
    } else {
        // Extract column names from metadata
        std::vector<std::string> names;
        names.reserve(columnCount);

        for (int i = 0; i < columnCount; ++i) {
            names.push_back(metadata.field(i).name());
        }

        setColumnNames(names);
    }

    m_initialized = true;
}

void SqlResult::init(int columnCount) {
    d_ptr->init(columnCount);
    m_currentRow = BeforeFirstRow;
    m_columnNames.clear();
    m_columnIndexes.clear();
}

void SqlResult::cleanup() {
    d_ptr->cleanup();
    m_currentRow = BeforeFirstRow;
    m_columnNames.clear();
    m_columnIndexes.clear();
    m_lastError = SqlError();
    m_closed = false;
    m_initialized = false;
    m_statement = nullptr;
}

bool SqlResult::close() {
    if (m_closed) {
        // Already closed
        return true;
    }

    // Mark as closed
    m_closed = true;

    // Remove ourselves from the statement's management
    if (m_statement) {
        m_statement->removeResult(this);
        // Clear the statement reference to break the circular reference
        m_statement = nullptr;
    }

    // We don't clear the cache so that cached results can still be accessed

    return true;
}

void SqlResult::clearValues() {
    m_currentRow = BeforeFirstRow;
    d_ptr->cache.clear();
    d_ptr->atEnd = false;
}

bool SqlResult::fetch(int row) {
    if (m_closed) {
        m_lastError = SqlError("Result set is closed", ErrorCode::ResultSetClosed);
        return false;
    }

    if (!m_initialized) {
        m_lastError = SqlError("Result set not initialized", ErrorCode::ResultInvalid);
        return false;
    }

    if (row < 0) {
        m_lastError = SqlError("Invalid row index", ErrorCode::InvalidArgument);
        return false;
    }

    // Already at the requested row
    if (m_currentRow == row) {
        return true;
    }

    // Handle forward-only result sets
    if (d_ptr->forwardOnly) {
        // Cannot move backward or if we're already past the end
        if (m_currentRow > row || m_currentRow == AfterLastRow) {
            return false;
        }

        // Skip rows until we reach the desired row
        SqlRecord record;
        while (m_currentRow < row) {
            if (!m_statement->fetchRow(record)) {
                m_lastError = SqlError("Failed to fetch row", ErrorCode::ExecutionFailed);
                m_currentRow = AfterLastRow;
                d_ptr->atEnd = true;
                return false;
            }

            // Only store the last record we need
            if (m_currentRow == row - 1) {
                // Update the cache with the new record
                d_ptr->addRecord(std::move(record));
            }

            m_currentRow++;
        }

        return true;
    }

    // Handle scrollable result sets

    // Check if the row is already in the cache
    if (d_ptr->canSeek(row)) {
        m_currentRow = row;
        return true;
    }

    // Cannot move backward beyond what's cached
    if (row < m_currentRow) {
        return false;
    }

    // Fetch more rows until we can seek to the desired row
    while (m_currentRow < row + 1) {
        if (!cacheNext()) {
            // Check if we've managed to cache enough rows
            if (d_ptr->canSeek(row)) {
                break;
            }
            return false;
        }
    }

    m_currentRow = row;
    m_lastError.clear();
    return true;
}

bool SqlResult::next() {
    // Validate state
    if (m_closed) {
        m_lastError = SqlError("Result set is closed", ErrorCode::ResultSetClosed);
        return false;
    }

    if (!m_initialized) {
        m_lastError = SqlError("Result set not initialized", ErrorCode::ResultInvalid);
        return false;
    }

    if (m_currentRow == AfterLastRow) {
        // Already at the end of the result set
        return false;
    }

    // Handle forward-only result sets
    if (d_ptr->forwardOnly) {
        // Check if we've already reached the end
        if (d_ptr->atEnd) {
            return false;
        }

        // Fetch the next row
        SqlRecord record;
        if (!m_statement->fetchRow(record)) {
            d_ptr->atEnd = true;
            m_currentRow = AfterLastRow;
            return false;
        }

        // Update the cache with the new record
        d_ptr->addRecord(std::move(record));
        m_currentRow++;
        return true;
    }

    // Handle scrollable result sets
    int nextRow = m_currentRow + 1;

    // Check if we need to fetch more rows
    if (nextRow >= d_ptr->cacheCount() && !d_ptr->atEnd) {
        if (!cacheNext()) {
            return false;
        }
    }

    // Check if we can move to the next row
    if (nextRow >= d_ptr->cacheCount()) {
        m_currentRow = AfterLastRow;
        return false;
    }

    m_currentRow = nextRow;
    m_lastError.clear();
    return true;
}

bool SqlResult::previous() {
    if (m_closed) {
        m_lastError = SqlError("Result set is closed", ErrorCode::ResultSetClosed);
        return false;
    }

    if (!m_initialized) {
        m_lastError = SqlError("Result set not initialized", ErrorCode::ResultInvalid);
        return false;
    }

    if (d_ptr->forwardOnly) {
        // Can't move backward in a forward-only result set
        m_lastError = SqlError("Cannot move backward in forward-only result set",
                              ErrorCode::NotImplemented);
        return false;
    }

    // If at the beginning, can't go previous
    if (m_currentRow <= BeforeFirstRow) {
        // Already at the beginning of the result set
        return false;
    }

    // Standard case: move to previous row
    m_currentRow--;
    return true;
}

bool SqlResult::first() {
    if (m_closed) {
        m_lastError = SqlError("Result set is closed", ErrorCode::ResultSetClosed);
        return false;
    }

    if (!m_initialized) {
        m_lastError = SqlError("Result set not initialized", ErrorCode::ResultInvalid);
        return false;
    }

    if (d_ptr->forwardOnly && m_currentRow != BeforeFirstRow) {
        // Can't move backward in a forward-only result set
        m_lastError = SqlError("Cannot move to first row in forward-only result set",
                              ErrorCode::NotImplemented);
        return false;
    }

    // Check if we already have data cached
    if (d_ptr->cache.empty() && !d_ptr->atEnd) {
        // Try to fetch at least one row
        if (!cacheNext()) {
            // Set proper error message if fetching failed
            if (!m_lastError.hasError()) {
                m_lastError = SqlError("No data available", ErrorCode::ResultInvalid);
            }
            return false;
        }
    }

    if (d_ptr->cache.empty()) {
        // No rows available
        m_lastError = SqlError("Empty result set", ErrorCode::ResultInvalid);
        return false;
    }

    // Move to the first row
    m_currentRow = 0;
    return true;
}

bool SqlResult::last() {
    if (m_closed) {
        m_lastError = SqlError("Result set is closed", ErrorCode::ResultSetClosed);
        return false;
    }

    if (!m_initialized) {
        m_lastError = SqlError("Result set not initialized", ErrorCode::ResultInvalid);
        return false;
    }

    if (d_ptr->forwardOnly) {
        // Can't move to last row in a forward-only result set
        m_lastError = SqlError("Cannot move to last row in forward-only result set",
                              ErrorCode::NotImplemented);
        return false;
    }

    // If we already have all rows cached and know we're at the end
    if (d_ptr->atEnd && !d_ptr->cache.empty()) {
        m_currentRow = d_ptr->cacheCount() - 1;
        return true;
    }

    // Fetch all remaining rows
    while (!d_ptr->atEnd) {
        if (!cacheNext()) {
            // If we couldn't fetch any more rows and there was an error other than EOF
            if (m_lastError.hasError()) {
                return false;
            }
            break;
        }
    }

    if (d_ptr->cache.empty()) {
        // No rows available
        m_lastError = SqlError("Empty result set", ErrorCode::ResultInvalid);
        return false;
    }

    m_currentRow = d_ptr->cacheCount() - 1;
    return true;
}

Variant SqlResult::value(int index) const {
    // Early validation to avoid unnecessary processing
    if (m_closed || !m_initialized ||
        m_currentRow < 0 || index < 0 || index >= d_ptr->columnCount ||
        m_currentRow >= d_ptr->cacheCount()) {
        return Variant();
    }

    // Get the value from the current record
    const SqlRecord& record = d_ptr->cache[m_currentRow];
    if (index >= static_cast<int>(record.size())) {
        return Variant();
    }

    // Return a copy of the value
    return record.value(index);
}

Variant SqlResult::value(std::string_view name) const {
    int index = getColumnIndex(name);
    if (index < 0) {
        return Variant();
    }

    return value(index);
}

SqlRecord SqlResult::record() const {
    if (m_currentRow < 0 || m_currentRow >= d_ptr->cacheCount() || !m_initialized) {
        return SqlRecord();
    }
    // Return a copy of the cached record
    return d_ptr->cache[m_currentRow];
}

bool SqlResult::isEmpty() const {
    if (!m_initialized) {
        return true;
    }
    return d_ptr->cache.empty() && d_ptr->atEnd;
}

bool SqlResult::isActive() const {
    return !m_closed && m_initialized;
}

int SqlResult::rowCount() const {
    if (!m_initialized) {
        return -1;
    }

    if (!d_ptr->atEnd) {
        // Can't determine row count until all rows are fetched
        return -1;
    }
    return d_ptr->cacheCount();
}

int SqlResult::currentRow() const {
    return m_currentRow;
}

int SqlResult::columnCount() const {
    return d_ptr->columnCount;
}

std::vector<std::string> SqlResult::columnNames() const {
    return m_columnNames;
}

bool SqlResult::isForwardOnly() const {
    return d_ptr->forwardOnly;
}

bool SqlResult::cacheNext() {
    // Early return if we can't fetch more rows
    if (d_ptr->atEnd || !m_initialized || !m_statement) {
        return false;
    }

    // Create record on the stack once instead of reallocating
    SqlRecord record;

    // Call the statement's fetchRow method directly
    if (!m_statement->fetchRow(record)) {
        d_ptr->atEnd = true;
        return false;
    }

    // Add the record to the cache (using move semantics)
    d_ptr->addRecord(std::move(record));
    return true;
}

int SqlResult::getColumnIndex(std::string_view name) const {
    auto it = m_columnIndexes.find(name);
    if (it == m_columnIndexes.end()) {
        return -1;
    }

    return it->second;
}

void SqlResult::setColumnNames(const std::vector<std::string>& names) {
    m_columnNames = names;
    m_columnIndexes.clear();
    m_columnIndexes.reserve(names.size());

    for (size_t i = 0; i < names.size(); i++) {
        m_columnIndexes[names[i]] = static_cast<int>(i);
    }
}

void SqlResult::setLastError(std::string_view message,
                             ErrorCode errorCode,
                             std::string_view sqlState) {
    m_lastError = SqlError(message, errorCode, sqlState);
}

void SqlResult::setLastError(const SqlError& error) {
    m_lastError = error;
}

const SqlError& SqlResult::lastError() const {
    return m_lastError;
}

} // namespace database
