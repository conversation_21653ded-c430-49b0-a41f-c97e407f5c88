﻿#ifndef DATABASE_SQL_TABLE_H
#define DATABASE_SQL_TABLE_H

#include <string>
#include <string_view>
#include <vector>
#include <memory>
#include <optional>
#include <ranges>
#include <concepts>

#include "sql_enums.h"
#include "sql_object_base.h"
#include "sql_concepts.h"

namespace database {

// Forward declarations
class SqlColumn;
class SqlIndex;
class SqlDriver;

/**
 * @brief Class representing a database table with dual-mode operation
 *
 * This class operates in two distinct modes:
 * 1. Builder Mode: Lightweight SQL statement construction without database access
 * 2. Metadata Mode: Full database metadata access via driver delegation
 *
 * The class automatically optimizes resource usage based on the operation mode.
 * Inherits common functionality from SqlObjectBase for consistency and optimization.
 */
class SqlTable final : public SqlObjectBase {
public:
    /**
     * @brief Optimized table metadata structure (no duplicate basic properties)
     */
    struct SqlTableMetadata {
        SqlObjectType type = SqlObjectType::Table;
        std::string catalog;  // Only catalog is stored here (name/schema in base)
        std::string comment;
        std::string createSql;
        std::vector<SqlColumn> columns;
        std::vector<SqlIndex> indexes;

        /**
         * @brief Get the primary key columns
         * @return The primary key column names
         */
        [[nodiscard]] std::vector<std::string> primaryKeyColumns() const {
            std::vector<std::string> pkColumns;

            for (const auto& column : columns) {
                if (column.isPrimaryKey()) {
                    pkColumns.push_back(column.name());
                }
            }

            return pkColumns;
        }

        /**
         * @brief Check if the table has a primary key
         * @return True if the table has a primary key, false otherwise
         */
        [[nodiscard]] bool hasPrimaryKey() const {
            return std::any_of(columns.begin(), columns.end(),
                               [](const auto& column) { return column.isPrimaryKey(); });
        }

        /**
         * @brief Find a column by name
         * @param columnName The column name to find
         * @return Pointer to the column metadata, or nullptr if not found
         */
        [[nodiscard]] const SqlColumn* findColumn(std::string_view columnName) const {
            auto it = std::find_if(columns.begin(), columns.end(),
                                   [columnName](const auto& column) {
                                       return column.name() == columnName;
                                   });

            return (it != columns.end()) ? &(*it) : nullptr;
        }

        /**
         * @brief Find an index by name
         * @param indexName The index name to find
         * @return Pointer to the index metadata, or nullptr if not found
         */
        [[nodiscard]] const SqlIndex* findIndex(std::string_view indexName) const {
            auto it = std::find_if(indexes.begin(), indexes.end(),
                                   [indexName](const auto& index) {
                                       return index.name() == indexName;
                                   });

            return (it != indexes.end()) ? &(*it) : nullptr;
        }

        /**
         * @brief Get the qualified table name (catalog.schema.table)
         * @return The qualified table name
         */
        [[nodiscard]] std::string qualifiedName() const {
            std::ostringstream oss;

            if (!catalog.empty()) {
                oss << catalog << ".";
            }

            if (!schema.empty()) {
                oss << schema << ".";
            }

            oss << name;

            return oss.str();
        }
    };

    /**
     * @brief Default constructor
     * Creates an empty table object
     */
    SqlTable() noexcept : SqlObjectBase(SqlObjectMode::Builder) {}

    /**
     * @brief Constructor with table name
     * @param name Table name
     */
    explicit SqlTable(std::string_view name) noexcept
        : SqlObjectBase(SqlObjectMode::Builder, CoreProperties(name)) {}

    /**
     * @brief Constructor with table name and driver
     * @param name Table name
     * @param driver Database driver (automatically enables metadata access)
     */
    SqlTable(std::string_view name, SqlDriver* driver) noexcept
        : SqlObjectBase(driver ? SqlObjectMode::Metadata : SqlObjectMode::Builder, CoreProperties(name), driver) {}

    /**
     * @brief Constructor with full properties
     * @param name Table name
     * @param schema Schema name
     * @param alias Table alias
     */
    SqlTable(std::string_view name, std::string_view schema, std::string_view alias = {}) noexcept
        : SqlObjectBase(SqlObjectMode::Builder, CoreProperties(name, schema, alias)) {}

    // Default copy/move operations
    SqlTable(const SqlTable& other) = default;
    SqlTable& operator=(const SqlTable& other) = default;
    SqlTable(SqlTable&& other) noexcept = default;
    SqlTable& operator=(SqlTable&& other) noexcept = default;

    // Basic Properties (inherited from SqlObjectBase)
    // Additional table-specific properties

    /**
     * @brief Get the catalog name
     * @return The catalog name
     */
    [[nodiscard]] const std::string& catalog() const noexcept;

    /**
     * @brief Set the catalog name
     * @param catalog The catalog name
     * @return Reference to this table for method chaining
     */
    SqlTable& setCatalog(std::string_view catalog);

    // Inherited setters with proper return type (fluent interface)
    SqlTable& setName(std::string_view name) {
        SqlObjectBase::setName(name);
        return *this;
    }

    SqlTable& setSchema(std::string_view schema) {
        SqlObjectBase::setSchema(schema);
        return *this;
    }

    SqlTable& setAlias(std::string_view alias) {
        SqlObjectBase::setAlias(alias);
        return *this;
    }

    SqlTable& as(std::string_view alias) {
        SqlObjectBase::as(alias);
        return *this;
    }

    SqlTable& setDriver(SqlDriver* driver) {
        SqlObjectBase::setDriver(driver);
        return *this;
    }

    /**
     * @brief Set the database driver for metadata operations
     * @param driver The database driver
     * @return Reference to this table for method chaining
     */
    SqlTable& setDriver(SqlDriver* driver) {
        m_driver = driver;
        if (driver) {
            m_mode = SqlObjectMode::Metadata;  // Switch to metadata mode when driver is set
        }
        return *this;
    }

    /**
     * @brief Get the table alias
     * @return The table alias
     */
    [[nodiscard]] const std::string& alias() const noexcept { return m_alias; }

    /**
     * @brief Check if the table has a schema
     * @return True if the table has a schema, false otherwise
     */
    [[nodiscard]] bool hasSchema() const noexcept { return !m_schema.empty(); }

    /**
     * @brief Check if the table has an alias
     * @return True if the table has an alias, false otherwise
     */
    [[nodiscard]] bool hasAlias() const noexcept { return !m_alias.empty(); }

    /**
     * @brief Set the table alias (Builder mode setter)
     * @param alias The table alias
     * @return Reference to this table for method chaining
     */
    SqlTable& setAlias(std::string_view alias) {
        m_alias = alias;
        return *this;
    }

    /**
     * @brief Set the table alias (fluent interface)
     * @param alias The table alias
     * @return Reference to this table for method chaining
     */
    SqlTable& as(std::string_view alias) {
        return setAlias(alias);
    }

    // Database Metadata Access (requires database driver)

    /**
     * @brief Check if the table exists in the database
     * @return True if the table exists, false otherwise
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] bool exists() const;

    /**
     * @brief Get the table type (lazy-loaded from database)
     * @return The table type, or nullopt if not available
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] std::optional<SqlObjectType> type() const;

    /**
     * @brief Get the table comment (lazy-loaded from database)
     * @return The table comment
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] const std::string& comment() const;

    /**
     * @brief Get the table creation SQL (lazy-loaded from database)
     * @return The table creation SQL
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] const std::string& createSql() const;

    // Column Operations

    /**
     * @brief Get all columns in the table (lazy-loaded from database)
     * @return Vector of SqlColumn objects
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] const std::vector<SqlColumn>& columns() const;

    /**
     * @brief Create a column reference for this table
     * @param name The column name
     * @return A column object associated with this table
     */
    [[nodiscard]] SqlColumn column(std::string_view name) const;

    /**
     * @brief Check if a column exists
     * @param columnName The column name
     * @return True if the column exists, false otherwise
     */
    [[nodiscard]] bool hasColumn(std::string_view columnName) const;

    /**
     * @brief Get the primary key columns
     * @return Vector of primary key column names
     */
    [[nodiscard]] std::vector<std::string> primaryKeyColumns() const;

    /**
     * @brief Check if the table has a primary key
     * @return True if the table has a primary key, false otherwise
     */
    [[nodiscard]] bool hasPrimaryKey() const;

    // Index Operations

    /**
     * @brief Get all indexes on the table (lazy-loaded)
     * @return Vector of SqlIndex objects
     */
    [[nodiscard]] const std::vector<SqlIndex>& indexes() const;

    /**
     * @brief Get a specific index by name
     * @param indexName The index name
     * @return Pointer to the index, or nullptr if not found
     */
    [[nodiscard]] const SqlIndex* index(std::string_view indexName) const;

    /**
     * @brief Check if an index exists
     * @param indexName The index name
     * @return True if the index exists, false otherwise
     */
    [[nodiscard]] bool hasIndex(std::string_view indexName) const;

    // Cache Management

    /**
     * @brief Force reload of metadata from the database
     * @return Reference to this table for method chaining
     */
    SqlTable& reload();

    /**
     * @brief Check if metadata has been loaded
     * @return True if metadata is loaded, false otherwise
     */
    [[nodiscard]] bool isMetadataLoaded() const noexcept { return m_metadataLoaded; }

    /**
     * @brief Preload all metadata to avoid lazy loading
     * @return Reference to this table for method chaining
     */
    SqlTable& preloadMetadata();

    /**
     * @brief Create a column with the * wildcard
     * @return A column object representing all columns
     */
    [[nodiscard]] SqlColumn all() const;

    /**
     * @brief Get the qualified name of the table (with schema and/or alias if present)
     * @return The qualified name
     */
    [[nodiscard]] std::string qualifiedName() const;

    /**
     * @brief Get the SQL representation of the table
     * @return The SQL string
     */
    [[nodiscard]] std::string toSql() const;

    // SQL Generation (for compatibility with existing SQL builder usage)

    /**
     * @brief Generate a simple SELECT statement
     * @param columns Columns to select (empty for all columns)
     * @return The SELECT SQL statement
     */
    [[nodiscard]] std::string selectSql(const std::vector<std::string>& columns = {}) const;

    /**
     * @brief Generate a CREATE TABLE statement (if metadata is available)
     * @param ifNotExists Whether to include IF NOT EXISTS clause
     * @return The CREATE TABLE SQL statement
     */
    [[nodiscard]] std::string createTableSql(bool ifNotExists = false) const;

    /**
     * @brief Generate a DROP TABLE statement
     * @param ifExists Whether to include IF EXISTS clause
     * @return The DROP TABLE SQL statement
     */
    [[nodiscard]] std::string dropTableSql(bool ifExists = false) const;

    // Comparison Operators

    /**
     * @brief Equality operator
     * @param other The table to compare with
     * @return True if the tables are equal, false otherwise
     */
    [[nodiscard]] bool operator==(const SqlTable& other) const noexcept {
        return m_name == other.m_name &&
               m_schema == other.m_schema &&
               m_alias == other.m_alias;
    }

    /**
     * @brief Inequality operator
     * @param other The table to compare with
     * @return True if the tables are not equal, false otherwise
     */
    [[nodiscard]] bool operator!=(const SqlTable& other) const noexcept {
        return !(*this == other);
    }

    // Internal Metadata Access (for advanced use cases)

    /**
     * @brief Get the internal metadata object (advanced usage)
     * @return Const reference to the metadata, or nullptr if not loaded
     * @note This is for advanced scenarios where direct metadata access is needed
     */
    [[nodiscard]] const SqlTableMetadata* metadata() const;

private:
    // Table-specific properties (catalog is not in base class)
    std::string m_catalog;

    // Lazy-loaded data (only used in Metadata mode, mutable for lazy loading in const methods)
    mutable bool m_metadataLoaded = false;
    mutable std::shared_ptr<SqlTableMetadata> m_metadata;

    // Helper methods (non-inline, implemented in .cpp)
    void loadMetadata() const;
    void ensureMetadataLoaded() const;

    // Override from base class
    void invalidateCache() override;

};

} // namespace database


#endif // DATABASE_SQL_TABLE_H
