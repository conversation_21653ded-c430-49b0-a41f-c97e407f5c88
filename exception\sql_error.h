#ifndef DATABASE_SQL_ERROR_H
#define DATABASE_SQL_ERROR_H

#include <string>
#include <string_view>

#include "sql_exception.h"

namespace database {

/**
 * @brief Non-exception class for database errors
 *
 * This class is used to record database errors without throwing exceptions.
 * It contains the same information as SqlException but doesn't interrupt
 * program flow.
 */
class SqlError {
public:
    /**
     * @brief Default constructor
     */
    SqlError() noexcept;

    /**
     * @brief Constructor
     * @param message The error message
     * @param errorCode The error code
     * @param sqlState The SQL state
     */
    SqlError(
        std::string_view message,
        ErrorCode errorCode = ErrorCode::Unknown,
        std::string_view sqlState = "") noexcept;

    /**
     * @brief Constructor from SqlException
     * @param exception The exception to convert
     */
    SqlError(const SqlException& exception) noexcept;

    /**
     * @brief Get the error code
     * @return The error code
     */
    [[nodiscard]] ErrorCode errorCode() const noexcept { return m_errorCode; }

    /**
     * @brief Get the SQL state
     * @return The SQL state
     */
    [[nodiscard]] std::string_view sqlState() const noexcept { return m_sqlState; }

    /**
     * @brief Get the error message
     * @return The error message
     */
    [[nodiscard]] std::string_view message() const noexcept { return m_message; }

    /**
     * @brief Check if an error has occurred
     * @return True if an error has occurred, false otherwise
     */
    [[nodiscard]] bool hasError() const noexcept { return !m_message.empty(); }

    /**
     * @brief Clear the error
     */
    void clear() noexcept;

    /**
     * @brief Convert to SqlException
     * @return A SqlException with the same information
     */
    [[nodiscard]] SqlException toException() const;

private:
    ErrorCode m_errorCode;
    std::string m_sqlState;
    std::string m_message;
};

/**
 * @brief Enumeration of error categories
 */
enum class ErrorCategory {
    General,
    Connection,
    Execution,
    Transaction,
    Constraint,
    Resource,
    Unknown
};

/**
 * @brief Get the category of an error code
 * @param code The error code
 * @return The error category
 */
[[nodiscard]] constexpr ErrorCategory getErrorCategoryEnum(ErrorCode code) noexcept {
    int value = static_cast<int>(code);
    if (value < 100) return ErrorCategory::General;
    if (value < 200) return ErrorCategory::Connection;
    if (value < 300) return ErrorCategory::Execution;
    if (value < 400) return ErrorCategory::Transaction;
    if (value < 500) return ErrorCategory::Constraint;
    if (value < 600) return ErrorCategory::Resource;
    return ErrorCategory::Unknown;
}

/**
 * @brief Get the category of an error code as a string
 * @param code The error code
 * @return A string representing the category
 */
[[nodiscard]] constexpr std::string_view getErrorCategory(ErrorCode code) noexcept {
    switch (getErrorCategoryEnum(code)) {
    case ErrorCategory::General:     return "General";
    case ErrorCategory::Connection:  return "Connection";
    case ErrorCategory::Execution:   return "Execution";
    case ErrorCategory::Transaction: return "Transaction";
    case ErrorCategory::Constraint:  return "Constraint";
    case ErrorCategory::Resource:    return "Resource";
    case ErrorCategory::Unknown:     return "Unknown";
    }
    return "Unknown"; // Unreachable, but needed to avoid compiler warnings
}

/**
 * @brief Check if an error code belongs to a specific category
 * @param code The error code
 * @param category The category to check
 * @return True if the error code belongs to the category, false otherwise
 */
[[nodiscard]] constexpr bool isErrorInCategory(ErrorCode code, ErrorCategory category) noexcept {
    return getErrorCategoryEnum(code) == category;
}

/**
 * @brief Check if an error code belongs to a specific category
 * @param code The error code
 * @param category The category to check as a string
 * @return True if the error code belongs to the category, false otherwise
 */
[[nodiscard]] inline bool isErrorInCategory(ErrorCode code, std::string_view category) noexcept {
    return getErrorCategory(code) == category;
}

/**
 * @brief Convert an error code to a descriptive string
 * @param code The error code
 * @return A string describing the error code
 */
[[nodiscard]] constexpr std::string_view errorCodeToString(ErrorCode code) noexcept {
    switch (code) {
    // General errors
    case ErrorCode::Unknown:              return "Unknown";
    case ErrorCode::InvalidArgument:      return "InvalidArgument";
    case ErrorCode::NotImplemented:       return "NotImplemented";
    case ErrorCode::OperationCancelled:   return "OperationCancelled";
    case ErrorCode::InternalError:        return "InternalError";

        // Connection errors
    case ErrorCode::ConnectionFailed:     return "ConnectionFailed";
    case ErrorCode::ConnectionTimeout:    return "ConnectionTimeout";
    case ErrorCode::ConnectionClosed:     return "ConnectionClosed";
    case ErrorCode::AuthenticationFailed: return "AuthenticationFailed";
    case ErrorCode::NetworkError:         return "NetworkError";

        // Execution errors
    case ErrorCode::ExecutionFailed:      return "ExecutionFailed";
    case ErrorCode::StatementInvalid:     return "StatementInvalid";
    case ErrorCode::ParameterBindingFailed: return "ParameterBindingFailed";
    case ErrorCode::ResultSetClosed:      return "ResultSetClosed";
    case ErrorCode::QueryTimeout:         return "QueryTimeout";
    case ErrorCode::SyntaxError:          return "SyntaxError";

        // Transaction errors
    case ErrorCode::TransactionFailed:    return "TransactionFailed";
    case ErrorCode::TransactionAlreadyActive: return "TransactionAlreadyActive";
    case ErrorCode::TransactionNotActive: return "TransactionNotActive";
    case ErrorCode::TransactionRollbackOnly: return "TransactionRollbackOnly";
    case ErrorCode::DeadlockDetected:     return "DeadlockDetected";

        // Constraint errors
    case ErrorCode::ConstraintViolation:  return "ConstraintViolation";
    case ErrorCode::UniqueConstraintViolation: return "UniqueConstraintViolation";
    case ErrorCode::ForeignKeyConstraintViolation: return "ForeignKeyConstraintViolation";
    case ErrorCode::CheckConstraintViolation: return "CheckConstraintViolation";
    case ErrorCode::NotNullConstraintViolation: return "NotNullConstraintViolation";

        // Resource errors
    case ErrorCode::ResourceError:        return "ResourceError";
    case ErrorCode::DatabaseLocked:       return "DatabaseLocked";
    case ErrorCode::ResultInvalid:        return "ResultInvalid";
    case ErrorCode::OutOfMemory:          return "OutOfMemory";
    case ErrorCode::DiskFull:             return "DiskFull";
    case ErrorCode::TooManyConnections:   return "TooManyConnections";
    }

    // We can't use std::to_string in a constexpr context, so we return a generic "Unknown" for unhandled values
    return "Unknown_ErrorCode";
}

/**
 * @brief Convert an error code to a descriptive string with numeric value for unknown codes
 * @param code The error code
 * @return A string describing the error code, including the numeric value for unknown codes
 */
[[nodiscard]] inline std::string errorCodeToDetailedString(ErrorCode code) {
    std::string_view basicString = errorCodeToString(code);
    if (basicString == "Unknown_ErrorCode") {
        return "Unknown(" + std::to_string(static_cast<int>(code)) + ")";
    }
    return std::string(basicString);
}

} // namespace database

#endif // DATABASE_SQL_ERROR_H
