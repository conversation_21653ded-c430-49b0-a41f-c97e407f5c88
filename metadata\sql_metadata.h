#ifndef DATABASE_SQL_METADATA_H
#define DATABASE_SQL_METADATA_H

#include <string>
#include <string_view>
#include <vector>
#include <memory>
#include <optional>

#include "driver/sql_field.h"

namespace database {

// Forward declarations
class SqlTable;
class SqlColumn;
class SqlIndex;

/**
 * @brief Enumeration of database object types
 */
enum class SqlObjectType {
    Table,              ///< Regular table
    View,               ///< Database view
    SystemTable,        ///< System table
    TemporaryTable,     ///< Temporary table
    MaterializedView,   ///< Materialized view
    Index,              ///< Index
    UniqueIndex,        ///< Unique index
    PrimaryKey,         ///< Primary key constraint
    ForeignKey,         ///< Foreign key constraint
    CheckConstraint,    ///< Check constraint
    Trigger,            ///< Database trigger
    Sequence            ///< Sequence/auto-increment
};

/**
 * @brief Enumeration of column constraints
 */
enum class SqlConstraintType {
    None,               ///< No constraint
    PrimaryKey,         ///< Primary key constraint
    Unique,             ///< Unique constraint
    NotNull,            ///< Not null constraint
    ForeignKey,         ///< Foreign key constraint
    Check,              ///< Check constraint
    Default,            ///< Default value constraint
    AutoIncrement       ///< Auto increment constraint
};

} // namespace database

#endif // DATABASE_SQL_METADATA_H 