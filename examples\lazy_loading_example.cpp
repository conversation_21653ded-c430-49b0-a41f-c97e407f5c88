#include <iostream>
#include <memory>
#include <chrono>

#include "driver/sqlite/sqlite_driver.h"
#include "connection/connection_params.h"
#include "builder/sql_table.h"
#include "builder/sql_column.h"
#include "builder/sql_index.h"

using namespace database;

void demonstrateLazyLoading() {
    // Create SQLite driver
    auto driver = createSqliteDriver();
    
    // Connect to in-memory database
    ConnectionParams params;
    params.setDatabase(":memory:");
    
    if (!driver->connect(params)) {
        std::cerr << "Failed to connect: " << driver->lastError().message() << std::endl;
        return;
    }
    
    // Create some test tables with complex structure
    driver->executeSimpleQuery(R"(
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            email TEXT UNIQUE,
            age INTEGER CHECK(age >= 0),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1,
            profile_data TEXT
        )
    )");
    
    driver->executeSimpleQuery(R"(
        CREATE TABLE posts (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            content TEXT,
            published_at DATETIME,
            view_count INTEGER DEFAULT 0,
            category_id INTEGER,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    )");
    
    driver->executeSimpleQuery(R"(
        CREATE TABLE categories (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            description TEXT,
            parent_id INTEGER,
            FOREIGN KEY (parent_id) REFERENCES categories(id)
        )
    )");
    
    // Create various indexes
    driver->executeSimpleQuery("CREATE INDEX idx_posts_user_id ON posts(user_id)");
    driver->executeSimpleQuery("CREATE UNIQUE INDEX idx_posts_title ON posts(title)");
    driver->executeSimpleQuery("CREATE INDEX idx_posts_published ON posts(published_at) WHERE published_at IS NOT NULL");
    driver->executeSimpleQuery("CREATE INDEX idx_posts_category ON posts(category_id)");
    driver->executeSimpleQuery("CREATE INDEX idx_users_email ON users(email)");
    
    std::cout << "=== 延迟加载和核心类API演示 ===" << std::endl;
    
    // 1. 基本表操作 - 无需加载元数据
    std::cout << "\n1. 基本表操作（无元数据加载）:" << std::endl;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // 创建表对象 - 这里不会触发数据库查询
    SqlTable usersTable = driver->table("users");
    SqlTable postsTable = driver->table("posts");
    SqlTable nonExistentTable = driver->table("non_existent");
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "创建表对象耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "users表名: " << usersTable.name() << std::endl;
    std::cout << "posts表名: " << postsTable.name() << std::endl;
    
    // 2. 存在性检查 - 这会触发数据库查询，但不加载完整元数据
    std::cout << "\n2. 存在性检查:" << std::endl;
    
    start = std::chrono::high_resolution_clock::now();
    bool usersExists = usersTable.exists();
    bool nonExistentExists = nonExistentTable.exists();
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "存在性检查耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "users表存在: " << (usersExists ? "是" : "否") << std::endl;
    std::cout << "non_existent表存在: " << (nonExistentExists ? "是" : "否") << std::endl;
    
    // 3. 延迟加载演示 - 第一次访问列信息时才加载
    std::cout << "\n3. 延迟加载演示:" << std::endl;
    
    std::cout << "元数据是否已加载: " << (usersTable.isMetadataLoaded() ? "是" : "否") << std::endl;
    
    start = std::chrono::high_resolution_clock::now();
    const auto& columns = usersTable.columns(); // 这里会触发元数据加载
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "首次加载列信息耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "元数据是否已加载: " << (usersTable.isMetadataLoaded() ? "是" : "否") << std::endl;
    std::cout << "列数量: " << columns.size() << std::endl;
    
    // 4. 缓存效果演示 - 第二次访问使用缓存
    start = std::chrono::high_resolution_clock::now();
    const auto& columnsAgain = usersTable.columns(); // 使用缓存，不会查询数据库
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "第二次访问列信息耗时: " << duration.count() << " 微秒（使用缓存）" << std::endl;
    
    // 5. 列详细信息访问
    std::cout << "\n4. 列详细信息:" << std::endl;
    
    for (const auto& column : columns) {
        std::cout << "  列: " << column.name() << std::endl;
        std::cout << "    类型: " << static_cast<int>(column.type()) 
                  << " (原生: " << column.nativeTypeName() << ")" << std::endl;
        std::cout << "    位置: " << column.position() << std::endl;
        std::cout << "    可空: " << (column.isNullable() ? "是" : "否") << std::endl;
        
        if (column.defaultValue().has_value()) {
            std::cout << "    默认值: " << column.defaultValue().value() << std::endl;
        }
        
        // 约束信息
        if (column.isPrimaryKey()) std::cout << "    [主键]" << std::endl;
        if (column.isUnique()) std::cout << "    [唯一]" << std::endl;
        if (column.isAutoIncrement()) std::cout << "    [自增]" << std::endl;
        
        std::cout << std::endl;
    }
    
    // 6. 索引信息延迟加载
    std::cout << "\n5. 索引信息延迟加载:" << std::endl;
    
    start = std::chrono::high_resolution_clock::now();
    const auto& indexes = postsTable.indexes(); // 延迟加载索引信息
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "加载索引信息耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "posts表索引数量: " << indexes.size() << std::endl;
    
    for (const auto& index : indexes) {
        std::cout << "  索引: " << index.name() << std::endl;
        std::cout << "    唯一: " << (index.isUnique() ? "是" : "否") << std::endl;
        std::cout << "    主键: " << (index.isPrimary() ? "是" : "否") << std::endl;
        
        const auto& indexColumns = index.columns();
        std::cout << "    列: ";
        for (const auto& col : indexColumns) {
            std::cout << col << " ";
        }
        std::cout << std::endl;
        
        if (!index.definition().empty()) {
            std::cout << "    定义: " << index.definition() << std::endl;
        }
        std::cout << std::endl;
    }
    
    // 7. 预加载演示
    std::cout << "\n6. 预加载演示:" << std::endl;
    
    SqlTable categoriesTable = driver->table("categories");
    std::cout << "categories表元数据是否已加载: " << (categoriesTable.isMetadataLoaded() ? "是" : "否") << std::endl;
    
    start = std::chrono::high_resolution_clock::now();
    categoriesTable.preloadMetadata(); // 预加载所有元数据
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "预加载元数据耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "categories表元数据是否已加载: " << (categoriesTable.isMetadataLoaded() ? "是" : "否") << std::endl;
    
    // 8. SQL生成（使用元数据）
    std::cout << "\n7. SQL生成:" << std::endl;
    
    std::cout << "SELECT语句: " << usersTable.selectSql() << std::endl;
    std::cout << "SELECT特定列: " << usersTable.selectSql({"id", "username", "email"}) << std::endl;
    std::cout << "DROP TABLE语句: " << usersTable.dropTableSql(true) << std::endl;
    
    // 9. 索引SQL生成
    std::cout << "\n8. 索引SQL生成:" << std::endl;
    
    SqlIndex newIndex = driver->index("idx_users_age", "users");
    newIndex.addColumn("age").setUnique(false);
    
    std::cout << "CREATE INDEX: " << newIndex.createSql(true) << std::endl;
    std::cout << "DROP INDEX: " << newIndex.dropSql(true) << std::endl;
    
    // 10. 获取所有表（延迟加载）
    std::cout << "\n9. 获取所有表:" << std::endl;
    
    start = std::chrono::high_resolution_clock::now();
    auto allTables = driver->getTables(); // 返回SqlTable对象，支持延迟加载
    end = std::chrono::high_resolution_clock::now();
    duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "获取所有表耗时: " << duration.count() << " 微秒" << std::endl;
    std::cout << "表数量: " << allTables.size() << std::endl;
    
    for (const auto& table : allTables) {
        std::cout << "  表: " << table.name() 
                  << " (元数据已加载: " << (table.isMetadataLoaded() ? "是" : "否") << ")" << std::endl;
    }
    
    // 11. 缓存失效和重新加载
    std::cout << "\n10. 缓存失效和重新加载:" << std::endl;
    
    std::cout << "重新加载前列数: " << usersTable.columns().size() << std::endl;
    
    // 添加新列
    driver->executeSimpleQuery("ALTER TABLE users ADD COLUMN phone TEXT");
    
    std::cout << "添加列后（使用缓存）列数: " << usersTable.columns().size() << std::endl;
    
    // 重新加载
    usersTable.reload();
    std::cout << "重新加载后列数: " << usersTable.columns().size() << std::endl;
    
    // 12. 高级元数据访问
    std::cout << "\n11. 高级元数据访问:" << std::endl;
    
    const auto* metadata = usersTable.metadata();
    if (metadata) {
        std::cout << "表类型: " << static_cast<int>(metadata->type()) << std::endl;
        std::cout << "主键列: ";
        auto pkColumns = metadata->primaryKeyColumns();
        for (const auto& pk : pkColumns) {
            std::cout << pk << " ";
        }
        std::cout << std::endl;
    }
    
    driver->disconnect();
}

int main() {
    try {
        demonstrateLazyLoading();
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 