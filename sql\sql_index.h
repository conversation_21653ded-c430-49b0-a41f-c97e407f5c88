﻿#ifndef DATABASE_SQL_INDEX_H
#define DATABASE_SQL_INDEX_H

#include <string>
#include <string_view>
#include <vector>
#include <memory>
#include <optional>
#include <ranges>
#include <concepts>

#include "sql_enums.h"
#include "sql_object_base.h"
#include "sql_concepts.h"

namespace database {

// Forward declarations
class SqlTable;
class SqlDriver;

/**
 * @brief Represents a database index with dual-mode operation
 *
 * This class operates in two distinct modes:
 * 1. Builder Mode: Lightweight SQL statement construction without database access
 * 2. Metadata Mode: Full database metadata access via driver delegation
 *
 * The class automatically optimizes resource usage based on the operation mode.
 * Inherits common functionality from SqlObjectBase for consistency and optimization.
 */
class SqlIndex final : public SqlObjectBase {
public:
    /**
     * @brief Optimized index metadata structure (no duplicate basic properties)
     */
    struct SqlIndexMetadata {
        std::string tableName;  // Table name (index name/schema in base)
        bool unique = false;
        bool primary = false;
        bool clustered = false;
        std::string indexType;
        std::vector<std::string> columns;
        std::string definition;
        std::string comment;
    };

    /**
     * @brief Default constructor
     * Creates an empty index object
     */
    SqlIndex() noexcept : SqlObjectBase(SqlObjectMode::Builder) {}

    /**
     * @brief Constructor with index name
     * @param name Index name
     */
    explicit SqlIndex(std::string_view name) noexcept
        : SqlObjectBase(SqlObjectMode::Builder, CoreProperties(name)) {}

    /**
     * @brief Constructor with index name and driver
     * @param name Index name
     * @param driver Database driver (automatically enables metadata access)
     */
    SqlIndex(std::string_view name, SqlDriver* driver) noexcept
        : SqlObjectBase(driver ? SqlObjectMode::Metadata : SqlObjectMode::Builder, CoreProperties(name), driver) {}

    /**
     * @brief Constructor with table reference
     * @param name Index name
     * @param table Parent table
     */
    SqlIndex(std::string_view name, const SqlTable* table) noexcept
        : SqlObjectBase(SqlObjectMode::Builder, CoreProperties(name)), m_table(table) {}

    // Default copy/move operations
    SqlIndex(const SqlIndex& other) = default;
    SqlIndex& operator=(const SqlIndex& other) = default;
    SqlIndex(SqlIndex&& other) noexcept = default;
    SqlIndex& operator=(SqlIndex&& other) noexcept = default;

    // Basic Properties (inherited from SqlObjectBase)
    // Additional index-specific properties

    /**
     * @brief Get the parent table
     * @return Pointer to the parent table, or nullptr if not set
     */
    [[nodiscard]] const SqlTable* table() const noexcept { return m_table; }

    /**
     * @brief Set the parent table
     * @param table The parent table
     * @return Reference to this index for method chaining
     */
    SqlIndex& setTable(const SqlTable& table);

    /**
     * @brief Get the table name this index belongs to
     * @return The table name
     */
    [[nodiscard]] std::string tableName() const;

    /**
     * @brief Set the table name this index belongs to (Builder mode setter)
     * @param tableName The table name
     * @return Reference to this index for method chaining
     */
    SqlIndex& setTableName(std::string_view tableName);

    /**
     * @brief Get the qualified table name (schema.table)
     * @return The qualified table name
     */
    [[nodiscard]] std::string qualifiedTableName() const;

    // Inherited setters with proper return type (fluent interface)
    SqlIndex& setName(std::string_view name) {
        SqlObjectBase::setName(name);
        return *this;
    }

    SqlIndex& setSchema(std::string_view schema) {
        SqlObjectBase::setSchema(schema);
        return *this;
    }

    SqlIndex& setAlias(std::string_view alias) {
        SqlObjectBase::setAlias(alias);
        return *this;
    }

    SqlIndex& as(std::string_view alias) {
        SqlObjectBase::as(alias);
        return *this;
    }

    SqlIndex& setDriver(SqlDriver* driver) {
        SqlObjectBase::setDriver(driver);
        return *this;
    }

    // Index Properties

    /**
     * @brief Check if the index is unique
     * @return True if unique, false otherwise (uses database metadata if driver is available)
     */
    [[nodiscard]] bool isUnique() const;

    /**
     * @brief Set whether the index is unique (for SQL building)
     * @param unique Whether the index is unique
     * @return Reference to this index for method chaining
     */
    SqlIndex& setUnique(bool unique) noexcept;

    /**
     * @brief Check if the index is primary (lazy-loaded from database)
     * @return True if primary, false otherwise
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] bool isPrimary() const;

    /**
     * @brief Check if the index is clustered (lazy-loaded from database)
     * @return True if clustered, false otherwise
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] bool isClustered() const;

    /**
     * @brief Get the index type
     * @return The index type (e.g., "BTREE", "HASH") - uses database metadata if driver is available
     */
    [[nodiscard]] const std::string& indexType() const;

    /**
     * @brief Set the index type (for SQL building)
     * @param type The index type
     * @return Reference to this index for method chaining
     */
    SqlIndex& setIndexType(std::string_view type);

    // Database Metadata Access (requires database driver)

    /**
     * @brief Get the index definition/creation SQL (lazy-loaded from database)
     * @return The index definition
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] const std::string& definition() const;

    /**
     * @brief Get the index comment (lazy-loaded from database)
     * @return The index comment
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] const std::string& comment() const;

    // Column Operations

    /**
     * @brief Get the column names in the index (lazy-loaded)
     * @return The column names
     */
    [[nodiscard]] const std::vector<std::string>& columns() const;

    /**
     * @brief Set the column names in the index (for SQL building)
     * @param columns The column names
     * @return Reference to this index for method chaining
     */
    SqlIndex& setColumns(const std::vector<std::string>& columns) {
        m_columnsOverride = columns;
        return *this;
    }

    /**
     * @brief Add a column to the index (for SQL building)
     * @param columnName The column name
     * @return Reference to this index for method chaining
     */
    SqlIndex& addColumn(std::string_view columnName);

    /**
     * @brief Clear all columns from the index (for SQL building)
     * @return Reference to this index for method chaining
     */
    SqlIndex& clearColumns() {
        m_columnsOverride = std::vector<std::string>{};
        return *this;
    }

    /**
     * @brief Get the WHERE clause for partial indexes (for SQL building)
     * @return The WHERE clause
     */
    [[nodiscard]] const std::string& whereClause() const;

    /**
     * @brief Set the WHERE clause for partial indexes (for SQL building)
     * @param whereClause The WHERE clause
     * @return Reference to this index for method chaining
     */
    SqlIndex& setWhereClause(std::string_view whereClause) {
        m_whereClauseOverride = whereClause;
        return *this;
    }

    // SQL Generation

    /**
     * @brief Generate CREATE INDEX SQL
     * @param ifNotExists Whether to include IF NOT EXISTS clause
     * @return The CREATE INDEX SQL statement
     */
    [[nodiscard]] std::string createSql(bool ifNotExists = false) const;

    /**
     * @brief Generate DROP INDEX SQL
     * @param ifExists Whether to include IF EXISTS clause
     * @return The DROP INDEX SQL statement
     */
    [[nodiscard]] std::string dropSql(bool ifExists = false) const;

    /**
     * @brief Check if the index definition is valid for SQL generation
     * @return True if valid, false otherwise
     */
    [[nodiscard]] bool isValid() const;

    // Cache Management

    /**
     * @brief Force reload of metadata from the database
     * @return Reference to this index for method chaining
     */
    SqlIndex& reload();

    /**
     * @brief Check if metadata has been loaded
     * @return True if metadata is loaded, false otherwise
     */
    [[nodiscard]] bool isMetadataLoaded() const noexcept { return m_metadataLoaded; }

    /**
     * @brief Preload all metadata to avoid lazy loading
     * @return Reference to this index for method chaining
     */
    SqlIndex& preloadMetadata();

    // Comparison Operators

    /**
     * @brief Equality operator
     * @param other The other index to compare with
     * @return True if equal, false otherwise
     */
    [[nodiscard]] bool operator==(const SqlIndex& other) const noexcept {
        return m_name == other.m_name &&
               tableName() == other.tableName() &&
               m_schema == other.m_schema;
    }

    /**
     * @brief Inequality operator
     * @param other The other index to compare with
     * @return True if not equal, false otherwise
     */
    [[nodiscard]] bool operator!=(const SqlIndex& other) const noexcept {
        return !(*this == other);
    }

    // Internal Metadata Access (for advanced use cases)

    /**
     * @brief Get the internal metadata object (advanced usage)
     * @return Const reference to the metadata, or nullptr if not loaded
     * @note This is for advanced scenarios where direct metadata access is needed
     */
    [[nodiscard]] const Metadata* metadata() const;

    /**
     * @brief Set the internal metadata (used by SqlTable during loading)
     * @param metadata The index metadata
     * @note This is an internal method used by SqlTable
     */
    void setMetadata(const Metadata& metadata);

private:
    // Index-specific properties (basic properties in base class)
    const SqlTable* m_table = nullptr;
    std::string m_tableName; // Fallback when table object is not available

    // Builder mode overrides (only used in Builder mode)
    std::optional<bool> m_uniqueOverride;
    std::optional<std::string> m_indexTypeOverride;
    std::optional<std::vector<std::string>> m_columnsOverride;
    std::optional<std::string> m_whereClauseOverride;

    // Lazy-loaded data (only used in Metadata mode, mutable for lazy loading in const methods)
    mutable bool m_metadataLoaded = false;
    mutable std::shared_ptr<SqlIndexMetadata> m_metadata;

    // Helper methods (non-inline, implemented in .cpp)
    void loadMetadata() const;
    void ensureMetadataLoaded() const;

    // Override from base class
    void invalidateCache() override;
};

} // namespace database

#endif // DATABASE_SQL_INDEX_H
