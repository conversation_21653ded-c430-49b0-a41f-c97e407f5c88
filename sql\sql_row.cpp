﻿#include "sql_row.h"

namespace database {

SqlRow::SqlRow(const std::unordered_map<std::string, Variant>& values)
    : m_values(values)
{
}

SqlRow::SqlRow(std::unordered_map<std::string, Variant>&& values) noexcept
    : m_values(std::move(values))
{
}

SqlRow::SqlRow(std::initializer_list<std::pair<std::string, Variant>> values)
    : m_values(values)
{
}

SqlRow& SqlRow::set(const SqlColumn& column, const Variant& value) {
    return set(column.qualifiedName(), value);
}

SqlRow& SqlRow::set(std::string_view columnName, const Variant& value) {
    m_values[std::string(columnName)] = value;
    return *this;
}

Variant SqlRow::value(const SqlColumn& column) const {
    return value(column.qualifiedName());
}

Variant SqlRow::value(std::string_view columnName) const {
    auto it = m_values.find(std::string(columnName));
    if (it != m_values.end()) {
        return it->second;
    }
    return Variant();
}

bool SqlRow::has(const SqlColumn& column) const {
    return has(column.qualifiedName());
}

bool SqlRow::has(std::string_view columnName) const {
    return m_values.find(std::string(columnName)) != m_values.end();
}

std::vector<std::string> SqlRow::columnNames() const {
    std::vector<std::string> names;
    names.reserve(m_values.size());

    for (const auto& [name, _] : m_values) {
        names.push_back(name);
    }

    return names;
}

} // namespace database
