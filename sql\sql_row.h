﻿#ifndef DATABASE_SQL_ROW_H
#define DATABASE_SQL_ROW_H

#include <string>
#include <string_view>
#include <unordered_map>
#include <vector>
#include <initializer_list>
#include <ranges>

#include "variant.h"

namespace database {

// Forward declaration
class SqlColumn;

/**
 * @brief Lightweight class representing a database row with column values
 *
 * This class serves as an efficient data container for database row data.
 * It provides fast access to column values by name or SqlColumn reference.
 * Optimized for performance in result processing scenarios.
 */
class SqlRow final {
public:
    /**
     * @brief Default constructor
     */
    SqlRow() = default;

    /**
     * @brief Constructor with column values (copy)
     * @param values The column values
     */
    explicit SqlRow(const std::unordered_map<std::string, Variant>& values);

    /**
     * @brief Constructor with column values (move)
     * @param values The column values
     */
    explicit SqlRow(std::unordered_map<std::string, Variant>&& values) noexcept;

    /**
     * @brief Constructor with initializer list (C++20 improvement)
     * @param values The column values as initializer list
     */
    SqlRow(std::initializer_list<std::pair<std::string, Variant>> values);

    /**
     * @brief Constructor from range of pairs (C++20 ranges support)
     * @param range Range of (name, value) pairs
     */
    template<std::ranges::input_range R>
    requires std::convertible_to<std::ranges::range_value_t<R>, std::pair<std::string, Variant>>
    explicit SqlRow(R&& range) {
        if constexpr (std::ranges::sized_range<R>) {
            m_values.reserve(std::ranges::size(range));
        }
        for (auto&& [name, value] : range) {
            m_values.emplace(std::move(name), std::move(value));
        }
    }

    /**
     * @brief Set a column value
     * @param column The column
     * @param value The value
     * @return Reference to this row for method chaining
     */
    SqlRow& set(const SqlColumn& column, const Variant& value);

    /**
     * @brief Set a column value
     * @param columnName The column name
     * @param value The value
     * @return Reference to this row for method chaining
     */
    SqlRow& set(std::string_view columnName, const Variant& value);

    /**
     * @brief Get a column value
     * @param column The column
     * @return The value
     */
    [[nodiscard]] Variant value(const SqlColumn& column) const;

    /**
     * @brief Get a column value
     * @param columnName The column name
     * @return The value
     */
    [[nodiscard]] Variant value(std::string_view columnName) const;

    /**
     * @brief Check if the row has a value for a column
     * @param column The column
     * @return True if the row has a value for the column, false otherwise
     */
    [[nodiscard]] bool has(const SqlColumn& column) const;

    /**
     * @brief Check if the row has a value for a column
     * @param columnName The column name
     * @return True if the row has a value for the column, false otherwise
     */
    [[nodiscard]] bool has(std::string_view columnName) const;

    /**
     * @brief Get all column names
     * @return The column names
     */
    [[nodiscard]] std::vector<std::string> columnNames() const;

    /**
     * @brief Get all column values
     * @return The column values
     */
    [[nodiscard]] const std::unordered_map<std::string, Variant>& values() const noexcept { return m_values; }

    /**
     * @brief Get the number of columns
     * @return The number of columns
     */
    [[nodiscard]] size_t size() const noexcept { return m_values.size(); }

    /**
     * @brief Check if the row is empty
     * @return True if the row is empty, false otherwise
     */
    [[nodiscard]] bool empty() const noexcept { return m_values.empty(); }

    /**
     * @brief Clear all column values
     */
    void clear() noexcept { m_values.clear(); }

private:
    std::unordered_map<std::string, Variant> m_values;
};

} // namespace database

#endif // DATABASE_SQL_ROW_H
