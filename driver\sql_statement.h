#ifndef DATABASE_SQL_STATEMENT_H
#define DATABASE_SQL_STATEMENT_H

#include <string_view>
#include <vector>
#include <memory>

#include "variant.h"
#include "sql_result.h"
#include "exception/sql_error.h"

namespace database {

// Forward declarations
class SqlStatementPrivate;

/**
 * @brief Interface for database statements
 *
 * This class defines the operations for preparing and executing SQL statements.
 * It is separated from result set handling to provide a clearer separation of concerns.
 */
class SqlStatement {
public:
    /**
     * @brief Default constructor
     */
    SqlStatement();

    /**
     * @brief Virtual destructor
     */
    virtual ~SqlStatement();

    //----------------------------------------------------------------------
    // Statement preparation methods
    //----------------------------------------------------------------------

    /**
     * @brief Prepare a SQL statement
     * @param sql The SQL statement to prepare
     * @return True if the statement was prepared successfully, false otherwise
     */
    virtual bool prepare(std::string_view sql);

    /**
     * @brief Get the prepared SQL query
     * @return The prepared SQL query
     */
    [[nodiscard]] std::string_view query() const noexcept;

    //----------------------------------------------------------------------
    // Statement execution methods
    //----------------------------------------------------------------------

    /**
     * @brief Execute a prepared statement
     * @return True if the statement was executed successfully, false otherwise
     */
    virtual bool execute() = 0;

    /**
     * @brief Execute the batch
     * @return True if the statement was executed successfully, false otherwise
     */
    virtual bool executeBatch() = 0;

    //----------------------------------------------------------------------
    // Parameter binding methods
    //----------------------------------------------------------------------

    /**
     * @brief Bind a value to a parameter by index
     * @param index The parameter index (0-based)
     * @param value The value to bind
     * @return True if the binding was successful, false otherwise
     */
    bool bindValue(int index, const Variant& value);

    /**
     * @brief Bind a value to a parameter using a placeholder
     * @param placeholder The parameter placeholder (can be "?", "$1", ":name", "@name", etc.)
     * @param value The value to bind
     * @return True if the binding was successful, false otherwise
     */
    bool bindValue(std::string_view placeholder, const Variant& value);

    /**
     * @brief Get the value bound to a parameter by index
     * @param index The parameter index (0-based)
     * @return The bound value
     */
    [[nodiscard]] Variant boundValue(int index) const;

    /**
     * @brief Get the value bound to a parameter by placeholder
     * @param placeholder The parameter placeholder
     * @return The bound value
     */
    [[nodiscard]] Variant boundValue(std::string_view placeholder) const;

    /**
     * @brief Get a reference to the bound values
     * @return Reference to the bound values
     */
    [[nodiscard]] std::vector<Variant>& boundValues();

    /**
     * @brief Get a reference to the bound batch values
     * @return Reference to the bound batch values
     */
    [[nodiscard]] std::vector<std::vector<Variant>>& boundBatchs();

    /**
     * @brief Clear all parameter bindings
     */
    virtual void clearBindings() noexcept;

    //----------------------------------------------------------------------
    // Batch execution methods
    //----------------------------------------------------------------------

    /**
     * @brief Add the current parameter values to the batch
     * @return True if the binding was successful, false otherwise
     */
    bool addBatch();

    /**
     * @brief Add parameter values to the batch by index
     * @param index The parameter index (0-based)
     * @param values The vector of values to add to the batch
     * @return True if the binding was successful, false otherwise
     */
    bool bindBatch(int index, const std::vector<Variant>& values);

    /**
     * @brief Add parameter values to the batch by placeholder
     * @param placeholder The parameter placeholder (can be "?", "$1", ":name", "@name", etc.)
     * @param values The vector of values to add to the batch
     * @return True if the binding was successful, false otherwise
     */
    bool bindBatch(std::string_view placeholder, const std::vector<Variant>& values);

    /**
     * @brief Clear the batch
     */
    virtual void clearBatch() noexcept;

    //----------------------------------------------------------------------
    // Common methods
    //----------------------------------------------------------------------

    /**
     * @brief Fetch a row from the result set
     * @param record The record to populate with the fetched row
     * @return True if successful, false if no more rows
     */
    virtual bool fetchRow(SqlRecord& record) = 0;

    /**
     * @brief Get the number of rows affected by the last execute, executeUpdate, or executeBatch
     * @return The number of affected rows, or -1 if not applicable
     */
    [[nodiscard]] virtual int numRowsAffected() const noexcept = 0;

    /**
     * @brief Close the statement and release resources
     * @return True if successful, false otherwise
     */
    virtual bool close() = 0;

    /**
     * @brief Get the result set from the executed statement
     * @return A shared pointer to the result set, or nullptr if no result set is available
     */
    [[nodiscard]] virtual std::shared_ptr<SqlResult> result() const = 0;

    /**
     * @brief Remove a result set from this statement's management
     * @param result The result set to remove
     */
    virtual void removeResult(SqlResult* result) noexcept = 0;

    /**
     * @brief Get the last error
     * @return The last error
     */
    [[nodiscard]] const SqlError& lastError() const noexcept;

    /**
     * @brief Clear the last error
     * This method clears any previous error state
     */
    void clearError() noexcept;

    /**
     * @brief Set the last error
     * @param message The error message
     * @param errorCode The error code
     * @param sqlState The SQL state
     */
    void setLastError(std::string_view message,
                      ErrorCode errorCode = ErrorCode::Unknown,
                      std::string_view sqlState = "");

protected:
    // Private implementation pointer
    std::unique_ptr<SqlStatementPrivate> d_ptr;
};

} // namespace database

#endif // DATABASE_SQL_STATEMENT_H
