﻿#include "sql_builder.h"

#include <format>

#include "sql_database.h"

namespace database {

SqlQuery SqlBuilder::toQuery(SqlDatabase& database) const {
    // Create a query with the SQL
    SqlQuery query(database, build());

    // Bind all parameters
    for (const auto& [name, value] : m_parameters) {
        query.bind(name, value);
    }

    return query;
}

SqlQuery SqlBuilder::execute(SqlDatabase& database) const {
    // Create and execute the query
    SqlQuery query = toQuery(database);
    query.execute();

    return query;
}

void SqlBuilder::addConditionParameters(const SqlCondition& condition) {
    // Add parameters from the condition to this builder
    for (const auto& [name, value] : condition.namedParameters()) {
        m_parameters[name] = value;
    }

    // Mark SQL as dirty since we've added parameters
    m_sqlDirty = true;
}

void SqlBuilder::addBuilderParameters(const SqlBuilder& builder) {
    // Add parameters from another builder to this builder
    for (const auto& [name, value] : builder.parameters()) {
        m_parameters[name] = value;
    }

    // Mark SQL as dirty since we've added parameters
    m_sqlDirty = true;
}

std::string SqlBuilder::generateParamName(std::string_view baseName) const {
    // Generate a unique parameter name based on the counter
    return std::format("{}{}", baseName, ++m_paramCounter);
}

std::string SqlBuilder::addParameter(std::string_view name, const Variant& value) {
    // Add the parameter to the map
    m_parameters[std::string{name}] = value;

    // Mark SQL as dirty since we've added a parameter
    m_sqlDirty = true;

    // Return the parameter placeholder
    return std::format(":{}", name);
}

std::string SqlBuilder::addParameter(const Variant& value) {
    // Generate a unique parameter name
    std::string name = generateParamName("param");

    // Add the parameter to the map
    return addParameter(name, value);
}

std::string SqlBuilder::join(std::span<const std::string> strings, std::string_view separator) {
    if (strings.empty()) {
        return "";
    }

    std::string result = strings[0];
    for (size_t i = 1; i < strings.size(); ++i) {
        result.append(separator).append(strings[i]);
    }

    return result;
}

std::string SqlBuilder::joinColumns(std::span<const Column> columns, std::string_view separator) {
    if (columns.empty()) {
        return "";
    }

    std::string result;
    bool first = true;

    for (const auto& column : columns) {
        if (column.name().empty()) {
            continue;  // Skip empty columns
        }

        if (!first) {
            result.append(separator);
        }

        result.append(columnToSql(column));
        first = false;
    }

    return result;
}

std::string SqlBuilder::columnToSql(const Column& column) {
    return column.toSql();
}

std::string SqlBuilder::tableToSql(const Table& table) {
    return table.toSql();
}

std::string SqlBuilder::conditionToSql(const SqlCondition& condition) {
    return condition.toSql();
}

} // namespace database
