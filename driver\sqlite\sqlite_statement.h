#ifndef DATABASE_SQLITE_STATEMENT_H
#define DATABASE_SQLITE_STATEMENT_H

#include <string_view>
#include <memory>
#include <mutex>
#include <unordered_set>

#include "driver/sql_statement.h"
#include "driver/sql_result.h"

// Forward declaration for SQLite
struct sqlite3;
struct sqlite3_stmt;

namespace database {

// Forward declarations
class SqlDatabase;
class SqliteDriver;
class SqliteStatementPrivate;

/**
 * @brief SQLite implementation of the SqlStatement interface
 *
 * This class provides access to SQLite statements through the SqlStatement interface.
 * It also handles the execution and result management for SQLite statements.
 */
class SqliteStatement : public SqlStatement {
public:
    /**
     * @brief Constructor
     * @param driver The SQLite driver
     * @throws std::invalid_argument if driver is null
     */
     explicit SqliteStatement(SqliteDriver* driver);

    /**
     * @brief Move constructor
     * @param other The statement to move
     */
    SqliteStatement(SqliteStatement&& other) noexcept;

    /**
     * @brief Move assignment operator
     * @param other The statement to move
     * @return Reference to this statement
     */
    SqliteStatement& operator=(SqliteStatement&& other) noexcept;

    // Disable copy operations
    SqliteStatement(const SqliteStatement&) = delete;
    SqliteStatement& operator=(const SqliteStatement&) = delete;

    /**
     * @brief Destructor
     */
    ~SqliteStatement() override;

    bool prepare(std::string_view sql) override;
    bool execute() override;
    bool executeBatch() override;
    void clearBatch() noexcept override;
    bool fetchRow(SqlRecord& record) override;
    [[nodiscard]] int numRowsAffected() const noexcept override;
    bool close() override;
    [[nodiscard]] std::shared_ptr<SqlResult> result() const override;

    /**
     * @brief Get the SQLite statement handle
     * @return The SQLite statement handle
     */
    [[nodiscard]] sqlite3_stmt* handle() const noexcept;

    /**
     * @brief Check if the statement is valid
     * @return True if valid, false otherwise
     */
    [[nodiscard]] bool isValid() const noexcept;

    /**
     * @brief Remove a result set from this statement's management
     * @param result The result set to remove
     */
    void removeResult(SqlResult* result) noexcept override;

private:
    /**
     * @brief Initialize column information for the result set
     */
    void initColumns();

    /**
     * @brief Bind a single parameter to the statement
     * @param index The parameter index (0-based)
     * @param value The value to bind
     * @return True if successful, false otherwise
     */
    bool bindParameter(int index, const Variant& value);

    /**
     * @brief Bind parameters to the statement
     * @return True if successful, false otherwise
     */
    bool bindParameters();

    /**
     * @brief Reset the statement for reuse
     */
    void resetStatement();

    /**
     * @brief Cleanup the statement and release resources
     */
    void cleanup();

    /**
     * @brief Create a result object for the executed statement
     * @return A shared pointer to the result set
     */
    [[nodiscard]] std::shared_ptr<SqlResult> createResult() const;

    // Private implementation
    std::unique_ptr<SqliteStatementPrivate> d_ptr;

    // The SQLite driver
    SqliteDriver* m_driver;

    // Active result sets
    std::unordered_set<SqlResult*> m_results;
    std::mutex m_resultsMutex;

    // Add and remove result sets
    void addActiveResult(SqlResult* result);
    void removeActiveResult(SqlResult* result);
};

} // namespace database

#endif // DATABASE_SQLITE_STATEMENT_H
