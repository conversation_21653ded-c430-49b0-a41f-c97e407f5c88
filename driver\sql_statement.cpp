#include "sql_statement.h"

#include <format>
#include <algorithm>

namespace database {

/**
 * @brief Private implementation class for SqlStatement
 *
 * This class provides the implementation details for parameter binding
 * in SQL statements. It supports various placeholder styles and efficient
 * parameter storage.
 */
class SqlStatementPrivate {
public:
    // Binding types
    enum class BindType {
        Unknown,
        Positional,  // ? style
        Named,       // :name, @name style
        Indexed      // $1, $2 style
    };

    // Placeholder holder structure
    struct PlaceholderInfo {
        std::string name;       // The placeholder name (without prefix)
        std::string fullName;   // The full placeholder text (with prefix)
        size_t position;        // Position in the SQL string
        size_t index;           // Index in the parameter list
        char prefix;            // Prefix character (':', '@', '$', '?')
        BindType type;          // The type of this placeholder

        // Comparison operator for sorting by position
        bool operator<(const PlaceholderInfo& other) const noexcept {
            return position < other.position;
        }
    };

    // Constructor with default initialization
    SqlStatementPrivate() = default;

    // Parse placeholders in the SQL query
    [[nodiscard]] bool parsePlaceholders(std::string_view sql);

    // Get the index for a placeholder
    [[nodiscard]] int getPlaceholderIndex(std::string_view placeholder) const;

    // Get the placeholder name from a placeholder string
    [[nodiscard]] std::string_view getPlaceholderName(std::string_view placeholder) const noexcept;

    // Original SQL query
    std::string m_sql;

    // Processed SQL query (with placeholders replaced for drivers that don't support them)
    std::string m_processedSql;

    // Binding type
    BindType m_bindType = BindType::Unknown;

    // Bound values
    std::vector<Variant> m_values;

    // Placeholder information
    std::vector<PlaceholderInfo> m_placeholders;

    // Map of placeholder names to indices with transparent lookup
    std::unordered_map<std::string, size_t, StringViewHash, std::equal_to<>> m_placeholderMap;

    // Current bind count
    size_t m_bindCount = 0;

    // Batch values
    std::vector<std::vector<Variant>> m_batchValues;

    // Batch size
    size_t m_batchSize = 0;

    // Last error
    SqlError m_lastError;
};

// Parse placeholders in the SQL query
bool SqlStatementPrivate::parsePlaceholders(std::string_view sql) {
    // Clear previous state
    m_sql.assign(sql);
    m_placeholders.clear();
    m_placeholderMap.clear();
    m_bindType = BindType::Unknown;
    m_bindCount = 0;

    if (sql.empty()) {
        return true;
    }

    // Find all placeholders
    size_t pos = 0;
    size_t positionalCount = 0;

    // Helper function to skip string literals
    auto skipStringLiteral = [&](char quote) -> bool {
        pos++; // Skip opening quote
        while (pos < sql.length()) {
            if (sql[pos] == quote) {
                // Check for escaped quote (double quote)
                if (pos + 1 < sql.length() && sql[pos + 1] == quote) {
                    pos += 2; // Skip escaped quote
                } else {
                    pos++; // Skip closing quote
                    return true;
                }
            } else {
                pos++;
            }
        }
        return false; // Unterminated string
    };

    // Helper function to skip single-line comment
    auto skipSingleLineComment = [&]() {
        while (pos < sql.length() && sql[pos] != '\n' && sql[pos] != '\r') {
            pos++;
        }
    };

    // Helper function to skip multi-line comment
    auto skipMultiLineComment = [&]() -> bool {
        pos += 2; // Skip /*
        while (pos + 1 < sql.length()) {
            if (sql[pos] == '*' && sql[pos + 1] == '/') {
                pos += 2; // Skip */
                return true;
            }
            pos++;
        }
        return false; // Unterminated comment
    };

    // Helper function to skip quoted identifier
    auto skipQuotedIdentifier = [&](char quote) -> bool {
        pos++; // Skip opening quote
        while (pos < sql.length()) {
            if (sql[pos] == quote) {
                // Check for escaped quote in identifier
                if (pos + 1 < sql.length() && sql[pos + 1] == quote) {
                    pos += 2; // Skip escaped quote
                } else {
                    pos++; // Skip closing quote
                    return true;
                }
            } else {
                pos++;
            }
        }
        return false; // Unterminated identifier
    };

    while (pos < sql.length()) {
        char ch = sql[pos];

        switch (ch) {
        case '\'':
        case '"': {
            // Handle string literals
            if (!skipStringLiteral(ch)) {
                // Unterminated string literal - this is a SQL syntax error
                // but we continue parsing to be tolerant
            }
            break;
        }
        case '`': {
            // Handle MySQL quoted identifiers
            if (!skipQuotedIdentifier(ch)) {
                // Unterminated identifier
            }
            break;
        }
        case '[': {
            // Handle SQL Server style quoted identifier
            pos++; // Skip [
            while (pos < sql.length() && sql[pos] != ']') {
                pos++;
            }
            if (pos < sql.length()) {
                pos++; // Skip ]
            }
            break;
        }
        case '-': {
            // Handle single-line comments (--)
            if (pos + 1 < sql.length() && sql[pos + 1] == '-') {
                skipSingleLineComment();
            } else {
                pos++;
            }
            break;
        }
        case '/': {
            // Handle multi-line comments (/* */)
            if (pos + 1 < sql.length() && sql[pos + 1] == '*') {
                if (!skipMultiLineComment()) {
                    // Unterminated comment
                }
            } else {
                pos++;
            }
            break;
        }
        case '?': {
            // Question mark placeholder
            PlaceholderInfo info;
            info.position = pos;
            info.type = BindType::Positional;
            info.name = std::to_string(positionalCount++);
            info.fullName = "?";
            info.prefix = '?';
            info.index = 0; // Will be set later during processing
            m_placeholders.push_back(std::move(info));
            pos++;
            break;
        }
        case ':': {
            if (pos + 1 < sql.length() && std::isalpha(sql[pos + 1])) {
                // Named placeholder with colon (:name)
                size_t start = pos + 1;
                size_t end = start;
                while (end < sql.length() && (std::isalnum(sql[end]) || sql[end] == '_')) {
                    end++;
                }
                if (end > start) {
                    PlaceholderInfo info;
                    info.position = pos;
                    info.type = BindType::Named;
                    info.name = std::string(sql.substr(start, end - start));
                    info.fullName = ":" + info.name;
                    info.prefix = ':';
                    info.index = 0; // Will be set later during processing
                    m_placeholders.push_back(std::move(info));
                    pos = end;
                } else {
                    pos++;
                }
            } else {
                pos++;
            }
            break;
        }
        case '@': {
            if (pos + 1 < sql.length() && std::isalpha(sql[pos + 1])) {
                // Named placeholder with at sign (@name)
                size_t start = pos + 1;
                size_t end = start;
                while (end < sql.length() && (std::isalnum(sql[end]) || sql[end] == '_')) {
                    end++;
                }
                if (end > start) {
                    PlaceholderInfo info;
                    info.position = pos;
                    info.type = BindType::Named;
                    info.name = std::string(sql.substr(start, end - start));
                    info.fullName = "@" + info.name;
                    info.prefix = '@';
                    info.index = 0; // Will be set later during processing
                    m_placeholders.push_back(std::move(info));
                    pos = end;
                } else {
                    pos++;
                }
            } else {
                pos++;
            }
            break;
        }
        case '$': {
            if (pos + 1 < sql.length() && std::isdigit(sql[pos + 1])) {
                // Indexed placeholder with dollar sign ($1, $2, ...)
                size_t start = pos + 1;
                size_t end = start;
                while (end < sql.length() && std::isdigit(sql[end])) {
                    end++;
                }
                if (end > start) {
                    PlaceholderInfo info;
                    info.position = pos;
                    info.type = BindType::Indexed;
                    info.name = std::string(sql.substr(start, end - start));
                    info.fullName = "$" + info.name;
                    info.prefix = '$';
                    info.index = 0; // Will be set later during processing
                    m_placeholders.push_back(std::move(info));
                    pos = end;
                } else {
                    pos++;
                }
            } else {
                pos++;
            }
            break;
        }
        default:
            pos++;
            break;
        }
    }

    // If no placeholders found, return success
    if (m_placeholders.empty()) {
        return true;
    }

    // Set the bind type based on the first placeholder
    m_bindType = m_placeholders[0].type;
    size_t paramIndex = 0;
    size_t maxIndexedParam = 0;

    // Process placeholders based on type and assign indices
    for (auto& placeholder : m_placeholders) {
        // Check for mixed placeholder types
        if (placeholder.type != m_bindType) {
            // Mixed placeholder types found - return false
            m_bindType = BindType::Unknown;
            m_placeholders.clear();
            m_lastError = SqlError(
                "Prepare failed: Multiple binding placeholders have been detected",
                ErrorCode::ParameterBindingFailed);
            return false;
        }

        switch (m_bindType) {
        case BindType::Positional: {
            // For ? style placeholders, each gets a sequential index
            placeholder.index = paramIndex;
            m_placeholderMap[placeholder.name] = paramIndex;
            paramIndex++;
            break;
        }
        case BindType::Indexed: {
            // For $1 style placeholders, use the specified index
            size_t specifiedIndex = std::stoul(placeholder.name) - 1; // Convert to 0-based
            placeholder.index = specifiedIndex;
            maxIndexedParam = std::max(maxIndexedParam, specifiedIndex + 1);
            m_placeholderMap[placeholder.name] = specifiedIndex;
            break;
        }
        default: { // BindType::Named
            // For :name or @name style placeholders, reuse same index for same name
            auto it = m_placeholderMap.find(placeholder.name);
            if (it != m_placeholderMap.end()) {
                placeholder.index = it->second;
            } else {
                placeholder.index = paramIndex;
                m_placeholderMap[placeholder.name] = paramIndex;
                paramIndex++;
            }
        }
        }
    }

    // Set bind count
    if (m_bindType == BindType::Indexed) {
        m_bindCount = maxIndexedParam;
    } else {
        m_bindCount = paramIndex;
    }

    return true;
}

// Get the index for a placeholder
int SqlStatementPrivate::getPlaceholderIndex(std::string_view placeholder) const {
    std::string_view name = getPlaceholderName(placeholder);
    auto it = m_placeholderMap.find(name);
    if (it != m_placeholderMap.end()) {
        return static_cast<int>(it->second);
    }
    return -1;
}

// Get the placeholder name from a placeholder string
std::string_view SqlStatementPrivate::getPlaceholderName(std::string_view placeholder) const noexcept {
    if (placeholder.empty()) {
        return "";
    }

    char prefix = placeholder[0];
    if (prefix == ':' || prefix == '@' || prefix == '$') {
        return placeholder.substr(1);
    } else if (prefix == '?') {
        return "0"; // For single ? placeholder
    } else {
        // Assume it's already a name without prefix
        return placeholder;
    }
}

// Implementation of SqlStatement methods
SqlStatement::SqlStatement() = default;
SqlStatement::~SqlStatement() = default;

bool SqlStatement::prepare(std::string_view sql) {
    // Ensure we have a private implementation
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }

    // Parse placeholders in the SQL query
    return d_ptr->parsePlaceholders(sql);
}

std::string_view SqlStatement::query() const noexcept {
    if (!d_ptr) {
        return "";
    }
    return d_ptr->m_sql;
}

bool SqlStatement::bindValue(int index, const Variant& value) {
    // Ensure we have a private implementation
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }
    if (d_ptr->m_bindType == SqlStatementPrivate::BindType::Unknown) {
        setLastError("Invalid binding type", ErrorCode::ParameterBindingFailed);
        return false;
    }

    if (index < 0) {
        setLastError("Invalid parameter index", ErrorCode::ParameterBindingFailed);
        return false;
    }

    // Resize the values vector if needed
    if (index >= static_cast<int>(d_ptr->m_values.size())) {
        d_ptr->m_values.resize(index + 1);
    }

    // Store the value
    d_ptr->m_values[index] = value;
    return true;
}

bool SqlStatement::bindValue(std::string_view placeholder, const Variant& value) {
    // Ensure we have a private implementation
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }
    if (d_ptr->m_bindType == SqlStatementPrivate::BindType::Unknown
        || d_ptr->m_bindType == SqlStatementPrivate::BindType::Positional) {
        setLastError("Invalid binding type", ErrorCode::ParameterBindingFailed);
        return false;
    }

    // Get the index for this placeholder
    int index = d_ptr->getPlaceholderIndex(placeholder);
    if (index < 0) {
        setLastError(
            std::format("Invalid placeholder: {}", placeholder),
            ErrorCode::ParameterBindingFailed);
        return false;
    }

    // Resize the values vector if needed
    if (index >= static_cast<int>(d_ptr->m_values.size())) {
        d_ptr->m_values.resize(index + 1);
    }

    // Store the value
    d_ptr->m_values[index] = value;
    return true;
}

Variant SqlStatement::boundValue(int index) const {
    if (!d_ptr) {
        return Variant();
    }

    if (index < 0 || index >= static_cast<int>(d_ptr->m_values.size())) {
        return Variant();
    }

    return d_ptr->m_values[index];
}

Variant SqlStatement::boundValue(std::string_view placeholder) const {
    if (!d_ptr) {
        return Variant();
    }

    int index = d_ptr->getPlaceholderIndex(placeholder);
    if (index < 0 || index >= static_cast<int>(d_ptr->m_values.size())) {
        return Variant();
    }

    return d_ptr->m_values[index];
}

std::vector<Variant>& SqlStatement::boundValues() {
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }
    return d_ptr->m_values;
}

std::vector<std::vector<Variant>>& SqlStatement::boundBatchs() {
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }
    return d_ptr->m_batchValues;
}

void SqlStatement::clearBindings() noexcept {
    if (d_ptr) {
        d_ptr->m_values.clear();
    }
}

bool SqlStatement::addBatch() {
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }

    // Verify parameter values are consistent with bind count
    if (d_ptr->m_bindCount > 0 && d_ptr->m_values.size() < d_ptr->m_bindCount) {
        setLastError("Not all parameters bound for batch", ErrorCode::InvalidArgument);
        return false;
    }

    // Add current parameter values to the batch
    d_ptr->m_batchValues.push_back(d_ptr->m_values);
    d_ptr->m_batchSize++;

    return true;
}

bool SqlStatement::bindBatch(int index, const std::vector<Variant>& values) {
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }
    if (d_ptr->m_bindType == SqlStatementPrivate::BindType::Unknown) {
        setLastError("Invalid binding type", ErrorCode::ParameterBindingFailed);
        return false;
    }

    if (index < 0) {
        setLastError("Invalid parameter index", ErrorCode::InvalidArgument);
        return false;
    }

    // Check if values is empty
    if (values.empty()) {
        setLastError("Empty batch values", ErrorCode::InvalidArgument);
        return false;
    }

    // Resize batch values if needed
    if (d_ptr->m_batchValues.empty()) {
        d_ptr->m_batchValues.resize(values.size());
    } else if (d_ptr->m_batchValues.size() != values.size()) {
        setLastError("Batch size mismatch", ErrorCode::InvalidArgument);
        return false;
    }

    // Add values to batch
    for (size_t i = 0; i < values.size(); i++) {
        // Resize the parameter vector if needed
        if (d_ptr->m_batchValues[i].size() <= static_cast<size_t>(index)) {
            d_ptr->m_batchValues[i].resize(index + 1);
        }
        d_ptr->m_batchValues[i][index] = values[i];
    }

    d_ptr->m_batchSize = std::max(d_ptr->m_batchSize, values.size());

    return true;
}

bool SqlStatement::bindBatch(std::string_view placeholder, const std::vector<Variant>& values) {
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }
    if (d_ptr->m_bindType == SqlStatementPrivate::BindType::Unknown
        || d_ptr->m_bindType == SqlStatementPrivate::BindType::Positional) {
        setLastError("Invalid binding type", ErrorCode::ParameterBindingFailed);
        return false;
    }

    // Get the index for this placeholder
    int index = d_ptr->getPlaceholderIndex(placeholder);
    if (index < 0) {
        setLastError(std::format("Invalid placeholder: {}", placeholder), ErrorCode::InvalidArgument);
        return false;
    }

    // Check if values is empty
    if (values.empty()) {
        setLastError("Empty batch values", ErrorCode::InvalidArgument);
        return false;
    }

    // Resize batch values if needed
    if (d_ptr->m_batchValues.empty()) {
        d_ptr->m_batchValues.resize(values.size());
    } else if (d_ptr->m_batchValues.size() != values.size()) {
        setLastError("Batch size mismatch", ErrorCode::InvalidArgument);
        return false;
    }

    // Add values to batch
    for (size_t i = 0; i < values.size(); i++) {
        // Resize the parameter vector if needed
        if (d_ptr->m_batchValues[i].size() <= static_cast<size_t>(index)) {
            d_ptr->m_batchValues[i].resize(index + 1);
        }
        d_ptr->m_batchValues[i][index] = values[i];
    }

    d_ptr->m_batchSize = std::max(d_ptr->m_batchSize, values.size());

    return true;
}

void SqlStatement::clearBatch() noexcept {
    if (d_ptr) {
        d_ptr->m_batchValues.clear();
        d_ptr->m_batchSize = 0;
    }
}

const SqlError& SqlStatement::lastError() const noexcept {
    if (!d_ptr) {
        static SqlError emptyError;
        return emptyError;
    }
    return d_ptr->m_lastError;
}

void SqlStatement::setLastError(std::string_view message,
                                ErrorCode errorCode,
                                std::string_view sqlState)
{
    if (!d_ptr) {
        d_ptr = std::make_unique<SqlStatementPrivate>();
    }
    d_ptr->m_lastError = SqlError(message, errorCode, sqlState);
}

} // namespace database
