#include "update_builder.h"

#include <sstream>
#include <format>

namespace database {

UpdateBuilder::UpdateBuilder() = default;

UpdateBuilder::UpdateBuilder(const Table& table) {
    m_table = table;
}

UpdateBuilder& UpdateBuilder::table(const Table& table) {
    m_table = table;
    m_sqlDirty = true;
    return *this;
}

UpdateBuilder& UpdateBuilder::set(const Column& column, const Variant& value) {
    m_columnValues[column] = value;
    m_sqlDirty = true;
    return *this;
}

UpdateBuilder& UpdateBuilder::setRow(const Row& row) {
    for (const auto& [name, value] : row.values()) {
        // Create a column from the column name
        m_columnValues[Column(name)] = value;
    }
    m_sqlDirty = true;
    return *this;
}

void UpdateBuilder::addWhereClause(const SqlCondition& condition, SqlLogicalOperator logicalOperator) {
    m_whereConditions.push_back(WhereClause{
        .condition = condition,
        .logicalOperator = logicalOperator,
        .isFirst = m_whereConditions.empty()
    });

    addConditionParameters(condition);
    m_sqlDirty = true;
}

UpdateBuilder& UpdateBuilder::where(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::And);
    return *this;
}

UpdateBuilder& UpdateBuilder::andWhere(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::And);
    return *this;
}

UpdateBuilder& UpdateBuilder::orWhere(const SqlCondition& condition) {
    addWhereClause(condition, SqlLogicalOperator::Or);
    return *this;
}

std::string UpdateBuilder::build() const {
    if (m_sqlDirty) {
        buildSql();
    }

    return m_cachedSql;
}

void UpdateBuilder::buildSql() const {
    if (m_columnValues.empty()) {
        throw std::invalid_argument("At least one column value is required for UPDATE queries");
    }

    std::ostringstream sql;

    // UPDATE clause
    sql << "UPDATE ";

    if (m_table.has_value()) {
        sql << m_table->name();
    } else {
        throw std::invalid_argument("Table is required for UPDATE queries");
    }

    // SET clause
    sql << " SET ";

    bool first = true;
    for (const auto& [column, value] : m_columnValues) {
        if (!first) {
            sql << ", ";
        }

        std::string paramName = generateParamName("update");
        m_parameters[paramName] = value;
        sql << std::format("{} = :{}", column.name(), paramName);

        first = false;
    }

    // WHERE clause
    if (!m_whereConditions.empty()) {
        sql << " WHERE ";

        first = true;
        for (const auto& where : m_whereConditions) {
            if (!first) {
                sql << std::format(" {} ", sqlLogicalOperatorToString(where.logicalOperator));
            }

            sql << conditionToSql(where.condition);

            first = false;
        }
    }

    m_cachedSql = sql.str();
    m_sqlDirty = false;
}

} // namespace database
