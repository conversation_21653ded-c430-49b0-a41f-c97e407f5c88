#ifndef DATABASE_SQL_RECORD_H
#define DATABASE_SQL_RECORD_H

#include "sql_field.h"
#include <vector>
#include <unordered_map>
#include <string_view>

namespace database {

/**
 * @brief Represents a database record/row
 *
 * This class encapsulates a database record with a collection of fields.
 * It provides methods for accessing and manipulating the fields and their values.
 */
class SqlRecord {
public:
    /**
     * @brief Default constructor
     *
     * Creates an empty record with no fields.
     */
    SqlRecord() = default;

    /**
     * @brief Constructor with fields
     *
     * @param fields The fields to initialize the record with
     */
    explicit SqlRecord(const std::vector<SqlField>& fields);

    /**
     * @brief Constructor with name-value map
     *
     * @param map The map of field names to values
     */
    explicit SqlRecord(const std::unordered_map<std::string, Variant>& map);

    // Copy semantics
    /**
     * @brief Copy constructor
     *
     * @param other The record to copy
     */
    SqlRecord(const SqlRecord& other) = default;

    /**
     * @brief Copy assignment operator
     *
     * @param other The record to copy
     * @return Reference to this record
     */
    SqlRecord& operator=(const SqlRecord& other) = default;

    // Move semantics
    /**
     * @brief Move constructor
     *
     * @param other The record to move from
     */
    SqlRecord(SqlRecord&& other) noexcept = default;

    /**
     * @brief Move assignment operator
     *
     * @param other The record to move from
     * @return Reference to this record
     */
    SqlRecord& operator=(SqlRecord&& other) noexcept = default;

    /**
     * @brief Equality operator
     *
     * Records are considered equal if they have the same number of fields
     * and all fields are equal.
     *
     * @param other The record to compare with
     * @return True if records are equal, false otherwise
     */
    bool operator==(const SqlRecord& other) const noexcept;

    /**
     * @brief Inequality operator
     *
     * @param other The record to compare with
     * @return True if records are not equal, false otherwise
     */
    bool operator!=(const SqlRecord& other) const noexcept;

    /**
     * @brief Get the value of a field by index
     *
     * @param index The index of the field
     * @return The value of the field
     * @throws std::out_of_range if the index is out of range
     */
    [[nodiscard]] const Variant& value(size_t index) const;

    /**
     * @brief Get the value of a field by name
     *
     * @param name The name of the field
     * @return The value of the field
     * @throws std::out_of_range if the field does not exist
     */
    [[nodiscard]] const Variant& value(std::string_view name) const;

    /**
     * @brief Set the value of a field by name
     *
     * If the field does not exist, it will be created.
     *
     * @param name The name of the field
     * @param value The new value for the field
     */
    void setValue(std::string_view name, const Variant& value);

    /**
     * @brief Set the value of a field by index
     *
     * @param index The index of the field
     * @param value The new value for the field
     * @throws std::out_of_range if the index is out of range
     */
    void setValue(size_t index, const Variant& value);

    /**
     * @brief Check if a field is null by index
     *
     * @param index The index of the field
     * @return True if the field is null, false otherwise
     * @throws std::out_of_range if the index is out of range
     */
    [[nodiscard]] bool isNull(size_t index) const;

    /**
     * @brief Check if a field is null by name
     *
     * @param name The name of the field
     * @return True if the field is null, false otherwise
     * @throws std::out_of_range if the field does not exist
     */
    [[nodiscard]] bool isNull(std::string_view name) const;

    /**
     * @brief Get the number of fields in the record
     *
     * @return The number of fields
     */
    [[nodiscard]] constexpr size_t size() const noexcept { return m_fields.size(); }

    /**
     * @brief Get a field by index
     *
     * @param index The index of the field
     * @return The field
     * @throws std::out_of_range if the index is out of range
     */
    [[nodiscard]] const SqlField& field(size_t index) const;

    /**
     * @brief Get a field by name
     *
     * @param name The name of the field
     * @return The field
     * @throws std::out_of_range if the field does not exist
     */
    [[nodiscard]] const SqlField& field(std::string_view name) const;

    /**
     * @brief Check if a field exists by name
     *
     * @param name The name of the field
     * @return True if the field exists, false otherwise
     */
    [[nodiscard]] bool hasField(std::string_view name) const;

    /**
     * @brief Add a field to the record
     *
     * If a field with the same name already exists, it will be updated.
     *
     * @param field The field to add
     */
    void addField(const SqlField& field);

    /**
     * @brief Replace a field at a specific position
     *
     * @param pos The position of the field to replace
     * @param field The new field
     * @throws std::out_of_range if the position is out of range
     */
    void replaceField(int pos, const SqlField& field);

    /**
     * @brief Insert a field at a specific position
     *
     * @param pos The position to insert the field at
     * @param field The field to insert
     * @throws std::out_of_range if the position is out of range
     */
    void insertField(int pos, const SqlField& field);

    /**
     * @brief Remove a field at a specific position
     *
     * @param pos The position of the field to remove
     * @throws std::out_of_range if the position is out of range
     */
    void removeField(int pos);

    /**
     * @brief Get all fields in the record
     *
     * @return A reference to the vector of fields
     */
    [[nodiscard]] const std::vector<SqlField>& fields() const noexcept { return m_fields; }

    /**
     * @brief Convert the record to a map of field names to values
     *
     * @return A map of field names to values
     */
    [[nodiscard]] std::unordered_map<std::string, Variant> toMap() const;

    /**
     * @brief Get the names of all fields in the record
     *
     * @return A vector of field names
     */
    [[nodiscard]] std::vector<std::string> fieldNames() const;

    /**
     * @brief Clear all fields from the record
     */
    void clear() noexcept;

    /**
     * @brief Check if the record is empty
     *
     * @return True if the record has no fields, false otherwise
     */
    [[nodiscard]] constexpr bool isEmpty() const noexcept { return m_fields.empty(); }

    /**
     * @brief Clear all field values in the record
     *
     * This sets all field values to null but keeps the field names.
     */
    void clearValues();

    /**
     * @brief Reserve space for fields
     *
     * This is an optimization to avoid reallocations when adding many fields.
     *
     * @param size Number of fields to reserve space for
     */
    constexpr void reserve(size_t size) noexcept { m_fields.reserve(size); }

private:
    std::vector<SqlField> m_fields;                      ///< The fields in the record
    std::unordered_map<std::string, size_t> m_fieldMap;  ///< Maps field names to their indices in m_fields
};

} // namespace database

#endif // DATABASE_SQL_RECORD_H
