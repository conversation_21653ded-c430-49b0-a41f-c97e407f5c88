#include <iostream>
#include <memory>

#include "driver/sqlite/sqlite_driver.h"
#include "connection/connection_params.h"
#include "builder/sql_table.h"
#include "builder/sql_index.h"
#include "metadata/sql_metadata.h"

using namespace database;

void demonstrateRefactoredAPI() {
    // Create SQLite driver
    auto driver = createSqliteDriver();
    
    // Connect to in-memory database
    ConnectionParams params;
    params.setDatabase(":memory:");
    
    if (!driver->connect(params)) {
        std::cerr << "Failed to connect: " << driver->lastError().message() << std::endl;
        return;
    }
    
    // Create some test tables with more complex structure
    driver->executeSimpleQuery(R"(
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            email TEXT UNIQUE,
            age INTEGER CHECK(age >= 0),
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT 1
        )
    )");
    
    driver->executeSimpleQuery(R"(
        CREATE TABLE posts (
            id INTEGER PRIMARY KEY,
            user_id INTEGER NOT NULL,
            title TEXT NOT NULL,
            content TEXT,
            published_at DATETIME,
            view_count INTEGER DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )
    )");
    
    driver->executeSimpleQuery(R"(
        CREATE TABLE tags (
            id INTEGER PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            color TEXT DEFAULT '#000000'
        )
    )");
    
    // Create indexes
    driver->executeSimpleQuery("CREATE INDEX idx_posts_user_id ON posts(user_id)");
    driver->executeSimpleQuery("CREATE UNIQUE INDEX idx_posts_title ON posts(title)");
    driver->executeSimpleQuery("CREATE INDEX idx_posts_published ON posts(published_at) WHERE published_at IS NOT NULL");
    
    std::cout << "=== 重构后的数据库元数据API演示 ===" << std::endl;
    
    // 1. 获取所有表的元数据
    std::cout << "\n1. 获取所有表的元数据:" << std::endl;
    auto tables = driver->getTables();
    for (const auto& tableMetadata : tables) {
        std::cout << "表: " << tableMetadata.name() 
                  << " (类型: " << static_cast<int>(tableMetadata.type()) << ")" << std::endl;
        std::cout << "  列数: " << tableMetadata.columns().size() << std::endl;
        std::cout << "  索引数: " << tableMetadata.indexes().size() << std::endl;
        std::cout << "  主键: " << (tableMetadata.hasPrimaryKey() ? "是" : "否") << std::endl;
    }
    
    // 2. 使用SqlTable对象检查表存在性
    std::cout << "\n2. 表存在性检查:" << std::endl;
    SqlTable usersTable = driver->table("users");
    SqlTable nonExistentTable = driver->table("non_existent");
    
    std::cout << "users表存在: " << (driver->tableExists(usersTable) ? "是" : "否") << std::endl;
    std::cout << "non_existent表存在: " << (driver->tableExists(nonExistentTable) ? "是" : "否") << std::endl;
    
    // 3. 获取详细的表元数据
    std::cout << "\n3. users表的详细元数据:" << std::endl;
    auto usersMetadata = driver->getTableMetadata(usersTable);
    
    std::cout << "表名: " << usersMetadata.name() << std::endl;
    std::cout << "模式: " << (usersMetadata.schema().empty() ? "默认" : usersMetadata.schema()) << std::endl;
    std::cout << "完整名称: " << usersMetadata.qualifiedName() << std::endl;
    
    // 4. 列信息详细展示
    std::cout << "\n4. users表的列信息:" << std::endl;
    for (const auto& column : usersMetadata.columns()) {
        std::cout << "  列: " << column.name() 
                  << " (位置: " << column.position() << ")" << std::endl;
        std::cout << "    类型: " << static_cast<int>(column.type()) 
                  << " (原生: " << column.nativeTypeName() << ")" << std::endl;
        std::cout << "    可空: " << (column.isNullable() ? "是" : "否") << std::endl;
        
        if (column.defaultValue().has_value()) {
            std::cout << "    默认值: " << column.defaultValue().value() << std::endl;
        }
        
        // 显示约束
        const auto& constraints = column.constraints();
        if (!constraints.empty()) {
            std::cout << "    约束: ";
            for (const auto& constraint : constraints) {
                std::cout << static_cast<int>(constraint.type()) << " ";
            }
            std::cout << std::endl;
        }
        
        // 便利方法
        if (column.isPrimaryKey()) std::cout << "    [主键]" << std::endl;
        if (column.isUnique()) std::cout << "    [唯一]" << std::endl;
        if (column.isAutoIncrement()) std::cout << "    [自增]" << std::endl;
    }
    
    // 5. 索引信息
    std::cout << "\n5. posts表的索引信息:" << std::endl;
    SqlTable postsTable = driver->table("posts");
    auto postsIndexes = driver->getIndexes(postsTable);
    
    for (const auto& indexMetadata : postsIndexes) {
        std::cout << "  索引: " << indexMetadata.name() << std::endl;
        std::cout << "    表: " << indexMetadata.tableName() << std::endl;
        std::cout << "    唯一: " << (indexMetadata.isUnique() ? "是" : "否") << std::endl;
        std::cout << "    主键: " << (indexMetadata.isPrimary() ? "是" : "否") << std::endl;
        std::cout << "    类型: " << indexMetadata.indexType() << std::endl;
        
        std::cout << "    列: ";
        for (const auto& col : indexMetadata.columns()) {
            std::cout << col << " ";
        }
        std::cout << std::endl;
        
        if (!indexMetadata.definition().empty()) {
            std::cout << "    定义: " << indexMetadata.definition() << std::endl;
        }
    }
    
    // 6. 索引存在性检查
    std::cout << "\n6. 索引存在性检查:" << std::endl;
    SqlIndex existingIndex = driver->index("idx_posts_user_id");
    SqlIndex nonExistentIndex = driver->index("idx_non_existent");
    
    std::cout << "idx_posts_user_id存在: " << (driver->indexExists(existingIndex) ? "是" : "否") << std::endl;
    std::cout << "idx_non_existent存在: " << (driver->indexExists(nonExistentIndex) ? "是" : "否") << std::endl;
    
    // 7. 获取特定索引的元数据
    std::cout << "\n7. idx_posts_title索引的详细信息:" << std::endl;
    SqlIndex titleIndex = driver->index("idx_posts_title");
    auto titleIndexMetadata = driver->getIndexMetadata(titleIndex);
    
    std::cout << "索引名: " << titleIndexMetadata.name() << std::endl;
    std::cout << "表名: " << titleIndexMetadata.tableName() << std::endl;
    std::cout << "唯一: " << (titleIndexMetadata.isUnique() ? "是" : "否") << std::endl;
    std::cout << "列: ";
    for (const auto& col : titleIndexMetadata.columns()) {
        std::cout << col << " ";
    }
    std::cout << std::endl;
    
    // 8. 元数据与SQL构建器对象的转换
    std::cout << "\n8. 元数据与SQL构建器对象的转换:" << std::endl;
    
    // TableMetadata -> SqlTable
    SqlTable tableFromMetadata = usersMetadata.toSqlTable();
    std::cout << "从元数据创建的表对象: " << tableFromMetadata.name() << std::endl;
    
    // IndexMetadata -> SqlIndex
    SqlIndex indexFromMetadata = titleIndexMetadata.toSqlIndex();
    std::cout << "从元数据创建的索引对象: " << indexFromMetadata.name() << std::endl;
    
    // 9. 使用SqlIndex生成SQL
    std::cout << "\n9. 使用SqlIndex生成SQL:" << std::endl;
    SqlIndex newIndex = driver->index("idx_users_email", "users");
    newIndex.addColumn("email").setUnique(true);
    
    std::cout << "CREATE INDEX SQL: " << newIndex.createSql(true) << std::endl;
    std::cout << "DROP INDEX SQL: " << newIndex.dropSql(true) << std::endl;
    
    // 10. 模式信息
    std::cout << "\n10. 模式信息:" << std::endl;
    auto schemas = driver->getSchemas();
    std::cout << "可用模式: ";
    for (const auto& schema : schemas) {
        std::cout << schema << " ";
    }
    std::cout << std::endl;
    
    // 11. 按类型过滤对象
    std::cout << "\n11. 按类型获取数据库对象:" << std::endl;
    
    auto views = driver->getTables("", SqlObjectType::View);
    std::cout << "视图数量: " << views.size() << std::endl;
    
    auto systemTables = driver->getTables("", SqlObjectType::SystemTable);
    std::cout << "系统表数量: " << systemTables.size() << std::endl;
    
    driver->disconnect();
}

int main() {
    try {
        demonstrateRefactoredAPI();
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 