#include "sqlite_statement.h"

#include <algorithm>
#include <mutex>
#include <unordered_set>
#include <stdexcept>
#include <sqlite3.h>

#include "sqlite_driver.h"
#include "sqlite_error.h"

namespace database {

// Private implementation class for SqliteStatement
class SqliteStatementPrivate {
public:
    SqliteStatementPrivate() = default;
    ~SqliteStatementPrivate() {
        finalize();
    }

    void finalize() noexcept {
        if (stmt) {
            sqlite3_finalize(stmt);
            stmt = nullptr;
        }
    }

    // SQLite statement handle
    sqlite3_stmt* stmt = nullptr;

    // Cached result object
    std::shared_ptr<SqlResult> result = nullptr;

    // Record containing column metadata
    SqlRecord recordInfo;

    // First row fetching flags - for optimization
    bool skippedStatus = false;
    bool skipRow = false;

    // State
    bool prepared = false;
    bool hasFirstRowBeenFetched = false;
};

SqliteStatement::SqliteStatement(SqliteDriver* driver)
    : d_ptr(std::make_unique<SqliteStatementPrivate>()),
    m_driver(driver) {

    if (!m_driver) {
        throw std::invalid_argument("Driver cannot be null");
    }

    m_driver->addActiveStatement(this);
}

SqliteStatement::SqliteStatement(SqliteStatement&& other) noexcept
    : d_ptr(std::move(other.d_ptr)),
    m_driver(other.m_driver) {

    if (m_driver) {
        m_driver->removeActiveStatement(&other);
        m_driver->addActiveStatement(this);
    }

    // Ensure the moved-from object is in a valid state
    other.m_driver = nullptr;
}

SqliteStatement& SqliteStatement::operator=(SqliteStatement&& other) noexcept {
    if (this != &other) {
        // Clean up current resources
        if (m_driver) {
            m_driver->removeActiveStatement(this);
        }

        // Move resources from other
        d_ptr = std::move(other.d_ptr);
        m_driver = other.m_driver;

        // Update driver's active statements
        if (m_driver) {
            m_driver->removeActiveStatement(&other);
            m_driver->addActiveStatement(this);
        }

        // Ensure the moved-from object is in a valid state
        other.m_driver = nullptr;
    }
    return *this;
}

SqliteStatement::~SqliteStatement() {
    // Close the statement
    close();
}

bool SqliteStatement::prepare(std::string_view sql) {
    // Clean up any previous statement
    cleanup();

    // First, let the base class parse the placeholders
    // This will populate the base class's d_ptr with placeholder information
    if (!SqlStatement::prepare(sql)) {
        return false;
    }

    // Get the database handle
    sqlite3* db = m_driver->getHandle();
    if (!db) {
        setLastError("Database not connected", ErrorCode::ConnectionClosed);
        return false;
    }

    // Prepare the statement
    const char* tail = nullptr;
    std::string sqlStr{sql}; // Create a std::string from the string_view
    int result = sqlite3_prepare_v2(
        db, sqlStr.c_str(), static_cast<int>(sqlStr.size()), &d_ptr->stmt, &tail);

    if (result != SQLITE_OK) {
        ErrorCode errorCode = mapSqliteErrorToErrorCode(result);
        std::string errorMsg = getSqliteErrorMessage(result, db);
        setLastError("Failed to prepare statement: " + errorMsg, errorCode);
        cleanup();
        return false;
    }

    // Check if there are multiple statements
    if (tail && *tail) {
        std::string_view remainingSQL(tail);
        // Find first non-whitespace character
        auto pos = remainingSQL.find_first_not_of(" \t\n\r\f\v");
        if (pos != std::string_view::npos) {
            setLastError(
                "Multiple statements are not supported",
                ErrorCode::StatementInvalid);
            cleanup();
            return false;
        }
    }

    // Build parameter map
    /*int paramCount = sqlite3_bind_parameter_count(d_ptr->stmt);
    d_ptr->parameters.resize(paramCount);
    d_ptr->parameterMap.clear();

    for (int i = 1; i <= paramCount; ++i) {
        const char* name = sqlite3_bind_parameter_name(d_ptr->stmt, i);
        if (name && *name) {
            // SQLite parameter names start with :, @, or $
            d_ptr->parameterMap[name] = i;
        }
    }*/

    d_ptr->prepared = true;
    return true;
}

bool SqliteStatement::execute() {
    if (!isValid()) {
        setLastError("Statement not prepared", ErrorCode::StatementInvalid);
        return false;
    }

    // Reset the statement
    // if (!reset()) {
    //     return false;
    // }

    // Reset the statement and clear bindings
    sqlite3_reset(d_ptr->stmt);
    sqlite3_clear_bindings(d_ptr->stmt);

    // Reset state
    d_ptr->skippedStatus = false;
    d_ptr->skipRow = false;
    d_ptr->hasFirstRowBeenFetched = false;

    // Clean up any previous results
    d_ptr->result.reset();

    // Bind parameters
    if (!bindParameters()) {
        return false;
    }

    // Execute first step to check if statement is valid
    int result = sqlite3_step(d_ptr->stmt);

    switch (result) {
    case SQLITE_ROW:
        // Statement executed successfully and returned rows
        d_ptr->skippedStatus = true;
        d_ptr->skipRow = true;
        d_ptr->hasFirstRowBeenFetched = true;
        break;

    case SQLITE_DONE:
        // Statement executed successfully but no rows returned
        d_ptr->skippedStatus = false;
        d_ptr->skipRow = false;
        break;

    default:
        // Handle error cases
        {
            sqlite3* db = sqlite3_db_handle(d_ptr->stmt);
            ErrorCode errorCode = mapSqliteErrorToErrorCode(result);
            std::string errorMsg = getSqliteErrorMessage(result, db);
            setLastError(errorMsg, errorCode);
            return false;
        }
    }

    // Check if this is a SELECT statement (we need to keep the statement alive for results)
    std::string_view sqlView = this->query();
    auto pos = sqlView.find_first_not_of(" \t\n\r");
    bool isSelect = false;

    if (pos != std::string_view::npos) {
        // Get the first word of the SQL statement
        auto endPos = sqlView.find_first_of(" \t\n\r", pos);
        std::string_view prefix = sqlView.substr(pos, endPos != std::string_view::npos ? endPos - pos : sqlView.length() - pos);

        // Convert to lowercase for comparison (using a temporary string)
        std::string prefixLower(prefix);
        std::transform(prefixLower.begin(), prefixLower.end(), prefixLower.begin(),
                       [](unsigned char c) { return std::tolower(c); });
        isSelect = (prefixLower == "select");
    }

    // Initialize columns if this is a query with results
    if (isSelect && d_ptr->hasFirstRowBeenFetched) {
        initColumns();
    } else if (!isSelect) {
        // For non-query statements like INSERT/UPDATE/DELETE, we can reset after getting affected rows
        // but we don't clear bindings here to allow reuse with the same parameters
        sqlite3_reset(d_ptr->stmt);
    }

    // Create result set - the SqliteResult constructor will execute the first step
    // d_ptr->result = std::make_shared<SqlResult>(m_driver, d_ptr->stmt);
    // // Check if there was an error during execution
    // if (d_ptr->result->lastError().hasError()) {
    //     d_ptr->lastError = d_ptr->result->lastError();
    //     return false;
    // }
    return true;

    // Mathold B
    /*

    // Execute the statement
    int result = sqlite3_step(m_statement);
    if (result != SQLITE_DONE && result != SQLITE_ROW) {
        setLastError("Error executing statement: " + std::string(sqlite3_errmsg(m_connection)), result);
        return false;
    }

    return true;*/
}

bool SqliteStatement::executeBatch() {
    if (!isValid()) {
        setLastError("Statement not prepared", ErrorCode::StatementInvalid);
        return false;
    }

    // Get the database handle
    sqlite3* db = m_driver->getHandle();
    if (!db) {
        setLastError("Not connected to database", ErrorCode::ConnectionClosed);
        return false;
    }

    // Get batch values
    const auto& batches = boundBatchs();
    if (batches.empty()) {
        // No batches to execute
        return true;
    }

    // Begin transaction if not already in one
    bool needsCommit = false;
    if (!m_driver->beginTransaction()) {
        return false;
    }
    needsCommit = true;

    // Clean up any previous results
    d_ptr->result.reset();

    try {
        // Execute each batch entry
        for (const auto& batchValues : batches) {
            // Reset statement and clear bindings
            sqlite3_reset(d_ptr->stmt);
            sqlite3_clear_bindings(d_ptr->stmt);

            // Bind parameters from the batch
            for (size_t i = 0; i < batchValues.size(); ++i) {
                if (!bindParameter(static_cast<int>(i + 1), batchValues[i])) {
                    if (needsCommit) {
                        m_driver->rollbackTransaction();
                    }
                    return false;
                }
            }

            // Execute
            int result = sqlite3_step(d_ptr->stmt);
            if (result != SQLITE_DONE && result != SQLITE_ROW) {
                std::string errorMsg = db ? sqlite3_errmsg(db) : "Unknown error";
                setLastError(
                    "Failed to execute batch statement: " + errorMsg,
                    ErrorCode::ExecutionFailed);

                if (needsCommit) {
                    m_driver->rollbackTransaction();
                }
                return false;
            }
        }

        // Commit transaction if we started one
        if (needsCommit) {
            if (!m_driver->commitTransaction()) {
                return false;
            }
        }

        return true;
    }
    catch (const std::exception& e) {
        // Handle any exceptions
        setLastError(
            "Exception during batch execution: " + std::string(e.what()),
            ErrorCode::ExecutionFailed);

        // Rollback if we started a transaction
        if (needsCommit) {
            m_driver->rollbackTransaction();
        }

        return false;
    }
}

void SqliteStatement::clearBatch() noexcept {
    SqlStatement::clearBatch();
}

bool SqliteStatement::close() {
    // Make a copy of the set to avoid iterator invalidation and deadlock
    std::unordered_set<SqlResult*> resultsCopy;
    {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        resultsCopy = m_results;
    }

    // Close all active result sets
    for (auto* result : resultsCopy) {
        if (result) {
            result->close();
        }
    }

    {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        if (!m_results.empty()) {
            std::cerr << "SqliteStatement::close: results is NOT empty during close." << std::endl;
            m_results.clear();
        }
    }

    if (m_driver) {
        m_driver->removeActiveStatement(this);
    }

    // Cleanup the statement
    cleanup();

    return true;
}

std::shared_ptr<SqlResult> SqliteStatement::result() const {
    // Create result object if needed
    if (!d_ptr->result/* && isValid() && d_ptr->hasFirstRowBeenFetched*/) {
        // Create the result with function adapter to fetchRow
        d_ptr->result = createResult();
    }

    return d_ptr->result;
}

bool SqliteStatement::isValid() const noexcept {
    return d_ptr && d_ptr->stmt && d_ptr->prepared;
}

sqlite3_stmt* SqliteStatement::handle() const noexcept {
    return d_ptr ? d_ptr->stmt : nullptr;
}

int SqliteStatement::numRowsAffected() const noexcept {
    if (!d_ptr || !d_ptr->stmt) {
        return -1;
    }

    sqlite3* db = sqlite3_db_handle(d_ptr->stmt);
    if (!db) {
        return -1;
    }

    return sqlite3_changes(db);
}

bool SqliteStatement::fetchRow(SqlRecord& record) {
    if (!isValid()) {
        setLastError("Invalid statement handle", ErrorCode::StatementInvalid);
        return false;
    }

    int result;
    // If the first sqlite3_step has been executed, use the saved result
    if (d_ptr->skipRow) {
        d_ptr->skipRow = false;
        if (!d_ptr->skippedStatus) {
            return false;
        }
        result = SQLITE_ROW;
    } else {
        // Execute the next step
        result = sqlite3_step(d_ptr->stmt);
    }

    switch (result) {
    case SQLITE_ROW: {
        // Got a row, fetch the values
        int columnCount = sqlite3_column_count(d_ptr->stmt);
        if (columnCount <= 0) {
            return false;
        }

        // Pre-allocate record capacity if needed
        if (record.size() != static_cast<size_t>(columnCount)) {
            record.clear();
            record.reserve(columnCount);

            // Pre-populate the record with empty fields to avoid reallocations
            for (int i = 0; i < columnCount; ++i) {
                const char* name = sqlite3_column_name(d_ptr->stmt, i);
                record.addField(SqlField(name ? name : "", Variant()));
            }
        }

        // Fetch values for each column in a single pass
        for (int i = 0; i < columnCount; ++i) {
            Variant value;

            int columnType = sqlite3_column_type(d_ptr->stmt, i);
            switch (columnType) {
            case SQLITE_INTEGER: {
                // Check if the integer can fit in a 32-bit int to save memory
                sqlite3_int64 int64Val = sqlite3_column_int64(d_ptr->stmt, i);
                if (int64Val >= std::numeric_limits<int>::min() &&
                    int64Val <= std::numeric_limits<int>::max()) {
                    value = static_cast<int>(int64Val);
                } else {
                    value = int64Val;
                }
                break;
            }
            case SQLITE_FLOAT:
                value = sqlite3_column_double(d_ptr->stmt, i);
                break;
            case SQLITE_TEXT: {
                const unsigned char* text = sqlite3_column_text(d_ptr->stmt, i);
                if (text) {
                    int bytes = sqlite3_column_bytes(d_ptr->stmt, i);
                    value = std::string(reinterpret_cast<const char*>(text), static_cast<size_t>(bytes));
                }
                break;
            }
            case SQLITE_BLOB: {
                const void* blob = sqlite3_column_blob(d_ptr->stmt, i);
                int bytes = sqlite3_column_bytes(d_ptr->stmt, i);
                if (blob && bytes > 0) {
                    value = std::string(static_cast<const char*>(blob), static_cast<size_t>(bytes));
                }
                break;
            }
            case SQLITE_NULL:
            default:
                // Leave value as null
                break;
            }

            // Set the value in the record
            record.setValue(i, std::move(value));
        }
        return true;
    }

    case SQLITE_DONE:
        // No more rows
        sqlite3_reset(d_ptr->stmt);
        return false;

    default:
        // Handle error cases
        sqlite3* db = sqlite3_db_handle(d_ptr->stmt);
        ErrorCode errorCode = mapSqliteErrorToErrorCode(result);
        std::string errorMsg = getSqliteErrorMessage(result, db);
        setLastError("Error fetching row: " + errorMsg, errorCode);
        sqlite3_reset(d_ptr->stmt);
        return false;
    }
}

void SqliteStatement::initColumns() {
    if (!isValid()) {
        return;
    }

    int columnCount = sqlite3_column_count(d_ptr->stmt);
    if (columnCount <= 0) {
        return;
    }

    // Pre-allocate column names vector to avoid reallocations
    std::vector<std::string> columnNames;
    columnNames.reserve(columnCount);

    // Clear the record info and reserve space
    d_ptr->recordInfo.clear();
    d_ptr->recordInfo.reserve(columnCount);

    // Process all columns in a single pass
    for (int i = 0; i < columnCount; ++i) {
        // Get column information
        const char* name = sqlite3_column_name(d_ptr->stmt, i);
        const char* tableName = sqlite3_column_table_name(d_ptr->stmt, i);
        const char* declType = sqlite3_column_decltype(d_ptr->stmt, i);

        // Create field with appropriate metadata
        std::string columnName = name ? name : "";
        columnNames.push_back(columnName);

        // Create field with appropriate metadata
        SqlField field(columnName, Variant());

        // Set table name if available
        if (tableName) {
            field.setTableName(tableName);
        }

        // Set field type based on declared type
        if (declType) {
            std::string typeName = declType;
            // Convert to lowercase for case-insensitive comparison
            std::transform(typeName.begin(), typeName.end(), typeName.begin(),
                           [](unsigned char c) { return std::tolower(c); });

            // Determine field type
            FieldType fieldType = FieldType::Unknown;

            if (typeName == "integer" || typeName == "int") {
                fieldType = FieldType::Integer;
            } else if (typeName == "bigint") {
                fieldType = FieldType::BigInt;
            } else if (typeName == "float") {
                fieldType = FieldType::Float;
            } else if (typeName == "double" || typeName == "real") {
                fieldType = FieldType::Double;
            } else if (typeName == "text") {
                fieldType = FieldType::Text;
            } else if (typeName == "blob") {
                fieldType = FieldType::Blob;
            } else if (typeName == "boolean" || typeName == "bool") {
                fieldType = FieldType::Boolean;
            } else if (typeName == "date") {
                fieldType = FieldType::Date;
            } else if (typeName == "time") {
                fieldType = FieldType::Time;
            } else if (typeName == "datetime" || typeName == "timestamp") {
                fieldType = FieldType::DateTime;
            } else if (typeName.find("char") != std::string::npos) {
                fieldType = FieldType::VarChar;
            }

            // Set the field type if determined
            if (fieldType != FieldType::Unknown) {
                field.setType(fieldType);
            }
        }

        // Add to record info
        d_ptr->recordInfo.addField(std::move(field));
    }
}

std::shared_ptr<SqlResult> SqliteStatement::createResult() const {
    // Create a result that will use this statement to fetch rows
    auto result = std::make_shared<SqlResult>();

    // Initialize the result with this statement as data source and the column metadata
    result->initialize(const_cast<SqliteStatement*>(this), d_ptr->recordInfo);

    // Add the result to our active results list
    const_cast<SqliteStatement*>(this)->addActiveResult(result.get());

    return result;
}

void SqliteStatement::addActiveResult(SqlResult* result) {
    if (result) {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        m_results.insert(result);
    }
}

void SqliteStatement::removeActiveResult(SqlResult* result) {
    if (result) {
        std::lock_guard<std::mutex> lock(m_resultsMutex);
        m_results.erase(result);
    }
}

void SqliteStatement::removeResult(SqlResult* result) noexcept {
    removeActiveResult(result);
}

bool SqliteStatement::bindParameters() {
    if (!isValid()) {
        setLastError("Statement not prepared", ErrorCode::StatementInvalid);
        return false;
    }

    // Get the SQLite parameter count
    int paramCount = sqlite3_bind_parameter_count(d_ptr->stmt);
    if (paramCount == 0) {
        // No parameters to bind
        return true;
    }

    // Handle named parameters
    bool hasNamedParameters = false;
    for (int i = 1; i <= paramCount; ++i) {
        const char* paramName = sqlite3_bind_parameter_name(d_ptr->stmt, i);
        if (paramName && *paramName) {
            hasNamedParameters = true;

            // Get the parameter name without the prefix
            std::string name = paramName;
            std::string placeholderName = name;
            if (!placeholderName.empty() && (placeholderName[0] == ':' ||
                                             placeholderName[0] == '@' ||
                                             placeholderName[0] == '$')) {
                placeholderName = placeholderName.substr(1);
            }

            // Try to find the value for this parameter
            Variant value = boundValue(name);
            if (value.isNull()) {
                value = boundValue(placeholderName);
            }

            // Bind the parameter if we have a value
            if (!value.isNull()) {
                if (!bindParameter(i, value)) {
                    return false;
                }
            }
        }
    }

    // Handle positional parameters
    if (!hasNamedParameters) {
        for (int i = 1; i <= paramCount; ++i) {
            // Convert SQLite's 1-based index to our 0-based index
            Variant value = boundValue(i - 1);
            if (!value.isNull()) {
                if (!bindParameter(i, value)) {
                    return false;
                }
            }
        }
    }

    return true;
}

bool SqliteStatement::bindParameter(int index, const Variant& value) {
    if (!isValid()) {
        setLastError("Statement not prepared", ErrorCode::StatementInvalid);
        return false;
    }

    // Check parameter index is valid
    int paramCount = sqlite3_bind_parameter_count(d_ptr->stmt);
    if (index <= 0 || index > paramCount) {
        setLastError(
            "Invalid parameter index: " + std::to_string(index) +
                " (valid range: 1-" + std::to_string(paramCount) + ")",
            ErrorCode::ParameterBindingFailed);
        return false;
    }

    int result = SQLITE_OK;

    if (value.isNull()) {
        // Bind NULL for null or void values
        result = sqlite3_bind_null(d_ptr->stmt, index);
    } else if (value.is<int>()) {
        result = sqlite3_bind_int(d_ptr->stmt, index, value.to<int>());
    } else if (value.is<int64_t>()) {
        result = sqlite3_bind_int64(d_ptr->stmt, index, value.to<int64_t>());
    } else if (value.is<double>()) {
        result = sqlite3_bind_double(d_ptr->stmt, index, value.to<double>());
    } else if (value.is<bool>()) {
        result = sqlite3_bind_int(d_ptr->stmt, index, value.to<bool>() ? 1 : 0);
    } else if (value.is<std::string>()) {
        const auto& str = value.to<std::string>();
        result = sqlite3_bind_text(d_ptr->stmt, index,
                                   str.c_str(),
                                   static_cast<int>(str.size()),
                                   SQLITE_TRANSIENT);
    } else {
        // Convert to string for other types
        const auto str = value.to<String>();
        result = sqlite3_bind_text(d_ptr->stmt, index,
                                   str.c_str(),
                                   static_cast<int>(str.size()),
                                   SQLITE_TRANSIENT);
    }

    if (result != SQLITE_OK) {
        sqlite3* db = sqlite3_db_handle(d_ptr->stmt);
        setLastError(
            "Failed to bind parameter " + std::to_string(index) + ": " +
            (db ? std::string(sqlite3_errmsg(db)) : "Unknown error"),
            ErrorCode::ParameterBindingFailed);
        return false;
    }

    return true;
}

void SqliteStatement::resetStatement() {
    if (d_ptr && d_ptr->stmt) {
        sqlite3_reset(d_ptr->stmt);
    }
}

void SqliteStatement::cleanup() {
    if (d_ptr) {
        d_ptr->finalize();
        d_ptr->result.reset();
        d_ptr->prepared = false;
        d_ptr->hasFirstRowBeenFetched = false;
        d_ptr->skippedStatus = false;
        d_ptr->skipRow = false;
        d_ptr->recordInfo.clear();
    }
}

} // namespace database
