﻿#ifndef DATABASE_SQL_COLUMN_H
#define DATABASE_SQL_COLUMN_H

#include <string>
#include <string_view>
#include <memory>
#include <compare>
#include <vector>
#include <optional>

#include "variant.h"
#include "sql_enums.h"
#include "driver/sql_field.h"

namespace database {

// Forward declarations
class SqlCondition;
class SqlColumn;
class SqlTable;
class SqlDriver;

/**
 * @brief Class representing a database column with dual-mode operation
 *
 * This class operates in two distinct modes:
 * 1. Builder Mode: Lightweight SQL statement construction without database access
 * 2. Metadata Mode: Full database metadata access via driver delegation
 *
 * The class automatically optimizes resource usage based on the operation mode.
 * Inherits common functionality from SqlObjectBase for consistency and optimization.
 */
class SqlColumn final : public SqlObjectBase {
public:
    /**
     * @brief Simple constraint metadata structure
     */
    struct ConstraintInfo {
        SqlConstraintType type = SqlConstraintType::None;
        std::string name;
        std::string definition;
        std::string referencedTable;
        std::vector<std::string> referencedColumns;
    };

    /**
     * @brief Simple column metadata structure
     */
    struct SqlColumnMetadata {
        std::string name;
        std::string alias;
        FieldType type = FieldType::Unknown;
        std::string nativeTypeName;
        bool nullable = true;
        std::optional<int> size;
        std::optional<int> precision;
        std::optional<int> scale;
        std::optional<std::string> defaultValue;
        std::string comment;
        int position = 0;
        std::vector<ConstraintInfo> constraints;

        /**
         * @brief Check if column has a specific constraint type
         * @param constraintType The constraint type to check
         * @return True if the constraint exists, false otherwise
         */
        [[nodiscard]] bool hasConstraint(SqlConstraintType constraintType) const noexcept {
            return std::any_of(constraints.begin(), constraints.end(),
                               [constraintType](const auto& constraint) {
                                   return constraint.type == constraintType;
                               });
        }

        /**
         * @brief Check if the column is a primary key
         * @return True if primary key, false otherwise
         */
        [[nodiscard]] bool isPrimaryKey() const noexcept { return hasConstraint(SqlConstraintType::PrimaryKey); }

        /**
         * @brief Check if the column is unique
         * @return True if unique, false otherwise
         */
        [[nodiscard]] bool isUnique() const noexcept { return hasConstraint(SqlConstraintType::Unique); }

        /**
         * @brief Check if the column is auto-increment
         * @return True if auto-increment, false otherwise
         */
        [[nodiscard]] bool isAutoIncrement() const noexcept { return hasConstraint(SqlConstraintType::AutoIncrement); }
    };

    /**
     * @brief Default constructor
     * Creates an empty column object
     */
    SqlColumn() noexcept : SqlObjectBase(SqlObjectMode::Builder) {}

    /**
     * @brief Constructor with column name
     * @param name Column name
     * @param table Parent table (optional)
     */
    explicit SqlColumn(std::string_view name, const SqlTable* table = nullptr)
        : SqlObjectBase(SqlObjectMode::Builder, CoreProperties(name)), m_table(table) {}

    /**
     * @brief Constructor with column name and driver
     * @param name Column name
     * @param table Parent table (optional)
     * @param driver Database driver (automatically enables metadata access)
     */
    SqlColumn(std::string_view name, const SqlTable* table, SqlDriver* driver)
        : SqlObjectBase(driver ? SqlObjectMode::Metadata : SqlObjectMode::Builder, CoreProperties(name), driver), m_table(table) {}

    // Default copy/move operations
    SqlColumn(const SqlColumn&) = default;
    SqlColumn& operator=(const SqlColumn&) = default;
    SqlColumn(SqlColumn&&) noexcept = default;
    SqlColumn& operator=(SqlColumn&&) noexcept = default;
    ~SqlColumn() = default;

    // Basic Properties (inherited from SqlObjectBase)
    // Additional column-specific properties

    /**
     * @brief Get the parent table
     * @return Pointer to the parent table, or nullptr if not set
     */
    [[nodiscard]] const SqlTable* table() const noexcept { return m_table; }

    /**
     * @brief Set the parent table
     * @param table The parent table
     * @return Reference to this column for method chaining
     */
    SqlColumn& setTable(const SqlTable* table) {
        m_table = table;
        return *this;
    }

    /**
     * @brief Check if the column has a table reference
     * @return True if the column has a table reference, false otherwise
     */
    [[nodiscard]] bool hasTable() const noexcept { return m_table != nullptr; }

    // Inherited setters with proper return type (fluent interface)
    SqlColumn& setName(std::string_view name) {
        SqlObjectBase::setName(name);
        return *this;
    }

    SqlColumn& setSchema(std::string_view schema) {
        SqlObjectBase::setSchema(schema);
        return *this;
    }

    SqlColumn& setAlias(std::string_view alias) {
        SqlObjectBase::setAlias(alias);
        return *this;
    }

    SqlColumn& as(std::string_view alias) {
        SqlObjectBase::as(alias);
        return *this;
    }

    SqlColumn& setDriver(SqlDriver* driver) {
        SqlObjectBase::setDriver(driver);
        return *this;
    }

    /**
     * @brief Get the qualified name of the column (with table alias if present)
     * @return The qualified name
     */
    [[nodiscard]] std::string qualifiedName() const;

    // Database Metadata Access (requires database driver)

    /**
     * @brief Get the column type (lazy-loaded from database)
     * @return The column type
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] FieldType type() const;

    /**
     * @brief Get the native database type name (lazy-loaded from database)
     * @return The native type name
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] const std::string& nativeTypeName() const;

    /**
     * @brief Check if the column is nullable (lazy-loaded from database)
     * @return True if nullable, false otherwise
     * @throws std::runtime_error if no database driver is set
     */
    [[nodiscard]] bool isNullable() const;

    /**
     * @brief Get the SQL representation of the column
     * @return The SQL string
     */
    [[nodiscard]] std::string toSql() const;

    /**
     * @brief Get the column size/length (lazy-loaded)
     * @return The column size, or nullopt if not applicable
     */
    [[nodiscard]] std::optional<int> size() const;

    /**
     * @brief Get the column precision (lazy-loaded)
     * @return The column precision, or nullopt if not applicable
     */
    [[nodiscard]] std::optional<int> precision() const;

    /**
     * @brief Get the column scale (lazy-loaded)
     * @return The column scale, or nullopt if not applicable
     */
    [[nodiscard]] std::optional<int> scale() const;

    /**
     * @brief Get the default value (lazy-loaded)
     * @return The default value, or nullopt if not set
     */
    [[nodiscard]] const std::optional<std::string>& defaultValue() const;

    /**
     * @brief Get the column comment (lazy-loaded)
     * @return The column comment
     */
    [[nodiscard]] const std::string& comment() const;

    /**
     * @brief Get the column position in the table (lazy-loaded)
     * @return The column position (1-based)
     */
    [[nodiscard]] int position() const;

    // Constraint Information (lazy-loaded)

    /**
     * @brief Check if column has a specific constraint type
     * @param type The constraint type to check
     * @return True if the constraint exists, false otherwise
     */
    [[nodiscard]] bool hasConstraint(SqlConstraintType type) const;

    /**
     * @brief Check if the column is a primary key
     * @return True if primary key, false otherwise
     */
    [[nodiscard]] bool isPrimaryKey() const;

    /**
     * @brief Check if the column is unique
     * @return True if unique, false otherwise
     */
    [[nodiscard]] bool isUnique() const;

    /**
     * @brief Check if the column is auto-increment
     * @return True if auto-increment, false otherwise
     */
    [[nodiscard]] bool isAutoIncrement() const;

    /**
     * @brief Get all constraints on this column (lazy-loaded)
     * @return The column constraints
     */
    [[nodiscard]] const std::vector<ConstraintInfo>& constraints() const;

    // SQL Generation

    /**
     * @brief Generate column definition SQL (for CREATE TABLE)
     * @return The column definition SQL
     */
    [[nodiscard]] std::string definitionSql() const;

    /**
     * @brief Generate ALTER TABLE ADD COLUMN SQL
     * @param tableName The table name (uses parent table if not specified)
     * @return The ALTER TABLE SQL
     */
    [[nodiscard]] std::string addColumnSql(const std::string& tableName = "") const;

    /**
     * @brief Generate ALTER TABLE DROP COLUMN SQL
     * @param tableName The table name (uses parent table if not specified)
     * @return The ALTER TABLE SQL
     */
    [[nodiscard]] std::string dropColumnSql(const std::string& tableName = "") const;

    // Cache Management

    /**
     * @brief Force reload of metadata from the database
     * @return Reference to this column for method chaining
     */
    SqlColumn& reload();

    /**
     * @brief Check if metadata has been loaded
     * @return True if metadata is loaded, false otherwise
     */
    [[nodiscard]] bool isMetadataLoaded() const noexcept { return m_metadata != nullptr; }

    /**
     * @brief Preload all metadata to avoid lazy loading
     * @return Reference to this column for method chaining
     */
    SqlColumn& preloadMetadata();

    /**
     * @brief Create an alias for the column
     * @param alias The column alias
     * @return Reference to this column for method chaining
     */
    SqlColumn& as(std::string_view alias) noexcept;

    /**
     * @brief Create a condition for equality
     * @param value The value to compare with
     * @return A condition object
     */
    [[nodiscard]] SqlCondition eq(const Variant& value) const;

    /**
     * @brief Create a condition for inequality
     * @param value The value to compare with
     * @return A condition object
     */
    [[nodiscard]] SqlCondition neq(const Variant& value) const;

    /**
     * @brief Create a condition for less than
     * @param value The value to compare with
     * @return A condition object
     */
    [[nodiscard]] SqlCondition lt(const Variant& value) const;

    /**
     * @brief Create a condition for less than or equal
     * @param value The value to compare with
     * @return A condition object
     */
    [[nodiscard]] SqlCondition lte(const Variant& value) const;

    /**
     * @brief Create a condition for greater than
     * @param value The value to compare with
     * @return A condition object
     */
    [[nodiscard]] SqlCondition gt(const Variant& value) const;

    /**
     * @brief Create a condition for greater than or equal
     * @param value The value to compare with
     * @return A condition object
     */
    [[nodiscard]] SqlCondition gte(const Variant& value) const;

    /**
     * @brief Create a condition for LIKE
     * @param pattern The pattern to match
     * @return A condition object
     */
    [[nodiscard]] SqlCondition like(std::string_view pattern) const;

    /**
     * @brief Create a condition for NOT LIKE
     * @param pattern The pattern to match
     * @return A condition object
     */
    [[nodiscard]] SqlCondition notLike(std::string_view pattern) const;

    /**
     * @brief Create a condition for IN
     * @param values The values to check against
     * @return A condition object
     */
    [[nodiscard]] SqlCondition in(const std::vector<Variant>& values) const;

    /**
     * @brief Create a condition for NOT IN
     * @param values The values to check against
     * @return A condition object
     */
    [[nodiscard]] SqlCondition notIn(const std::vector<Variant>& values) const;

    /**
     * @brief Create a condition for BETWEEN
     * @param min The minimum value
     * @param max The maximum value
     * @return A condition object
     */
    [[nodiscard]] SqlCondition between(const Variant& min, const Variant& max) const;

    /**
     * @brief Create a condition for IS NULL
     * @return A condition object
     */
    [[nodiscard]] SqlCondition isNull() const;

    /**
     * @brief Create a condition for IS NOT NULL
     * @return A condition object
     */
    [[nodiscard]] SqlCondition isNotNull() const;

    /**
     * @brief Create a condition with a string operator and value
     * @param op The operator as a string (e.g., "=", "<>", "LIKE", etc.)
     * @param value The value to compare with
     * @return A condition object
     */
    [[nodiscard]] SqlCondition condition(std::string_view op, const Variant& value) const;

    /**
     * @brief Create a condition with a string operator (for unary operators)
     * @param op The operator as a string (e.g., "IS NULL", "IS NOT NULL")
     * @return A condition object
     */
    [[nodiscard]] SqlCondition condition(std::string_view op) const;

    /**
     * @brief Create a condition with a string operator and multiple values
     * @param op The operator as a string (e.g., "IN", "NOT IN", "BETWEEN")
     * @param values The values to check against
     * @return A condition object
     */
    [[nodiscard]] SqlCondition condition(std::string_view op, const std::vector<Variant>& values) const;

    // Comparison Operators

    /**
     * @brief Three-way comparison operator (spaceship operator)
     * @param other The column to compare with
     * @return The comparison result
     */
    [[nodiscard]] std::strong_ordering operator<=>(const SqlColumn& other) const noexcept;

    /**
     * @brief Equality operator
     * @param other The other column to compare with
     * @return True if equal, false otherwise
     */
    [[nodiscard]] bool operator==(const SqlColumn& other) const noexcept {
        return m_name == other.m_name && m_table == other.m_table;
    }

    /**
     * @brief Inequality operator
     * @param other The other column to compare with
     * @return True if not equal, false otherwise
     */
    [[nodiscard]] bool operator!=(const SqlColumn& other) const noexcept {
        return !(*this == other);
    }

    /**
     * @brief Less than operator for creating SQL conditions
     * @param value The value to compare with
     * @return A condition object
     */
    // [[nodiscard]] SqlCondition operator<(const Variant& value) const {
    //     return lt(value);
    // }

    /**
     * @brief Less than or equal operator for creating SQL conditions
     * @param value The value to compare with
     * @return A condition object
     */
    // [[nodiscard]] SqlCondition operator<=(const Variant& value) const {
    //     return lte(value);
    // }

    /**
     * @brief Greater than operator for creating SQL conditions
     * @param value The value to compare with
     * @return A condition object
     */
    // [[nodiscard]] SqlCondition operator>(const Variant& value) const {
    //     return gt(value);
    // }

    /**
     * @brief Greater than or equal operator for creating SQL conditions
     * @param value The value to compare with
     * @return A condition object
     */
    // [[nodiscard]] SqlCondition operator>=(const Variant& value) const {
    //     return gte(value);
    // }

    /**
     * @brief Get the internal metadata object (advanced usage)
     * @return Const reference to the metadata, or nullptr if not loaded
     * @note This is for advanced scenarios where direct metadata access is needed
     */
    [[nodiscard]] const SqlColumnMetadata* metadata() const;

    /**
     * @brief Set the internal metadata (used by SqlTable during loading)
     * @param metadata The column metadata
     * @note This is an internal method used by SqlTable
     */
    void setMetadata(const SqlColumnMetadata& metadata);

private:
    // Column-specific properties (basic properties in base class)
    const SqlTable* m_table = nullptr;

    // Lazy-loaded data (only used in Metadata mode, mutable for lazy loading in const methods)
    mutable std::shared_ptr<SqlColumnMetadata> m_metadata { nullptr };

    // Helper methods (non-inline, implemented in .cpp)
    void loadMetadata() const;
    void ensureMetadataLoaded() const;

    // Override from base class
    void invalidateCache() override;
};

} // namespace database

// Hash function for SqlColumn to use it as a key in unordered_map
/*namespace std {
template<>
struct hash<database::SqlColumn> {
    [[nodiscard]] size_t operator()(const database::SqlColumn& column) const noexcept {
        // Hash the column name
        size_t h1 = std::hash<std::string>{}(column.name());

        // Hash the table if it exists
        size_t h2 = 0;
        if (column.hasTable()) {
            // Calculate table hash directly
            const auto& table = column.table();
            size_t tableNameHash = std::hash<std::string>{}(table->name());
            size_t tableAliasHash = table->hasAlias() ? std::hash<std::string>{}(table->alias()) : 0;
            h2 = tableNameHash ^ (tableAliasHash << 1);
        }

        // Hash the alias if it exists
        size_t h3 = column.hasAlias() ? std::hash<std::string>{}(column.alias()) : 0;

        return h1 ^ (h2 << 1) ^ (h3 << 2);
    }
};
}*/

#endif // DATABASE_SQL_COLUMN_H
