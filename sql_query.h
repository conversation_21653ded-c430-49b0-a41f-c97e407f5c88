#ifndef DATABASE_SQL_QUERY_H
#define DATABASE_SQL_QUERY_H

#include <memory>
#include <string>
#include <string_view>
#include <vector>

#include "variant.h"
#include "driver/sql_statement.h"
#include "driver/sql_result.h"
#include "exception/sql_error.h"

namespace database {

// Forward declarations
class SqlDatabase;
class SqlQueryPrivate;

/**
 * @brief High-level class for SQL query operations
 *
 * This class encapsulates both SqlStatement and SqlResult to provide
 * a convenient API with method chaining support for SQL operations.
 * It simplifies common database operations by providing a fluent interface.
 */
class SqlQuery {
public:
    /**
     * @brief Default constructor
     *
     * Creates an invalid query. A database must be set before using the query.
     */
    SqlQuery();

    /**
     * @brief Constructor with database
     * @param database The database to use for the query
     */
    explicit SqlQuery(SqlDatabase& database);

    /**
     * @brief Constructor with database and SQL query
     * @param database The database to use for the query
     * @param query The SQL query string
     */
    SqlQuery(SqlDatabase& database, std::string_view query);

    /**
     * @brief Move constructor
     * @param other The query to move from
     */
    SqlQuery(SqlQuery&& other) noexcept;

    /**
     * @brief Move assignment operator
     * @param other The query to move from
     * @return Reference to this query
     */
    SqlQuery& operator=(SqlQuery&& other) noexcept;

    /**
     * @brief Destructor
     */
    ~SqlQuery();

    // Delete copy constructor and copy assignment operator
    SqlQuery(const SqlQuery&) = delete;
    SqlQuery& operator=(const SqlQuery&) = delete;

    /**
     * @brief Set the database for the query
     * @param database The database to use
     * @return Reference to this query for method chaining
     */
    SqlQuery& setDatabase(SqlDatabase& database);

    /**
     * @brief Set the SQL query string
     * @param query The SQL query string
     * @return Reference to this query for method chaining
     */
    SqlQuery& setQuery(std::string_view query);

    /**
     * @brief Prepare the SQL statement
     * @return Reference to this query for method chaining
     */
    SqlQuery& prepare();

    /**
     * @brief Bind a value to a positional parameter (?)
     * @param position The parameter position (1-based)
     * @param value The value to bind
     * @return Reference to this query for method chaining
     */
    SqlQuery& bind(int position, const Variant& value);

    /**
     * @brief Bind a value to a named parameter (:name or @name)
     * @param name The parameter name
     * @param value The value to bind
     * @return Reference to this query for method chaining
     */
    SqlQuery& bind(std::string_view name, const Variant& value);

    /**
     * @brief Clear all parameter bindings
     * @return Reference to this query for method chaining
     */
    SqlQuery& clearBindings();

    /**
     * @brief Execute the query
     * @return Reference to this query for method chaining
     */
    SqlQuery& execute();

    /**
     * @brief Add a batch of parameters for batch execution
     * @return Reference to this query for method chaining
     */
    SqlQuery& addBatch();

    /**
     * @brief Execute the batch
     * @return Reference to this query for method chaining
     */
    SqlQuery& executeBatch();

    /**
     * @brief Clear the batch
     * @return Reference to this query for method chaining
     */
    SqlQuery& clearBatch();

    /**
     * @brief Move to the next row in the result set
     * @return True if successful, false if no more rows
     */
    bool next();

    /**
     * @brief Move to the first row in the result set
     * @return True if successful, false if the result set is empty
     */
    bool first();

    /**
     * @brief Move to the last row in the result set
     * @return True if successful, false if the result set is empty
     */
    bool last();

    /**
     * @brief Move to a specific row in the result set
     * @param row The row index (0-based)
     * @return True if successful, false if the row is out of range
     */
    bool seek(int row);

    /**
     * @brief Get the value of a column by index
     * @param index The column index (0-based)
     * @return The column value
     */
    Variant value(int index) const;

    /**
     * @brief Get the value of a column by name
     * @param name The column name
     * @return The column value
     */
    Variant value(std::string_view name) const;

    /**
     * @brief Get the current record
     * @return The current record
     */
    SqlRecord record() const;

    /**
     * @brief Get the number of rows affected by the last execute, executeUpdate, or executeBatch
     * @return The number of affected rows, or -1 if not applicable
     */
    int numRowsAffected() const;

    /**
     * @brief Get the number of rows in the result set
     * @return The number of rows, or -1 if not applicable
     */
    int rowCount() const;

    /**
     * @brief Get the number of columns in the result set
     * @return The number of columns, or 0 if not applicable
     */
    int columnCount() const;

    /**
     * @brief Get the column names
     * @return The column names
     */
    std::vector<std::string> columnNames() const;

    /**
     * @brief Check if the query is valid
     * @return True if valid, false otherwise
     */
    bool isValid() const;

    /**
     * @brief Check if the query is active
     * @return True if active, false otherwise
     */
    bool isActive() const;

    /**
     * @brief Get the last error
     * @return The last error
     */
    const SqlError& lastError() const;

    /**
     * @brief Close the query and release resources
     * @return Reference to this query for method chaining
     */
    SqlQuery& close();

    /**
     * @brief Get the underlying SQL statement
     * @return The SQL statement
     */
    std::shared_ptr<SqlStatement> statement() const;

    /**
     * @brief Get the underlying SQL result
     * @return The SQL result
     */
    std::shared_ptr<SqlResult> result() const;

private:
    // Private implementation
    std::unique_ptr<SqlQueryPrivate> d_ptr;
};

} // namespace database

#endif // DATABASE_SQL_QUERY_H
