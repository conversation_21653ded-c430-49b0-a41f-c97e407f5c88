#ifndef DATABASE_SQLITE_DRIVER_H
#define DATABASE_SQLITE_DRIVER_H

#include <memory>
#include <string_view>
#include <vector>

#include "driver/sql_driver.h"

// Forward declaration for SQLite
struct sqlite3;

namespace database {

// Forward declarations
class SqlDatabase;
class SqlStatement;
class SqliteDriverPrivate;
class SqliteStatement;

/**
 * @brief SQLite implementation of the SqlDriver interface
 *
 * This class provides access to SQLite databases through the SqlDriver interface.
 */
class SqliteDriver : public SqlDriver {
public:
    /**
     * @brief Constructor
     */
    SqliteDriver();

    /**
     * @brief Destructor
     */
    ~SqliteDriver() noexcept override;

    // Disable copy operations
    SqliteDriver(const SqliteDriver&) = delete;
    SqliteDriver& operator=(const SqliteDriver&) = delete;

    // Move operations
    SqliteDriver(SqliteDriver&&) noexcept = default;
    SqliteDriver& operator=(SqliteDriver&&) noexcept = default;

    bool connect(const ConnectionParams& params) override;
    bool disconnect() override;
    [[nodiscard]] bool isConnected() const noexcept override;
    ValidationResult validateConnection(std::string_view query = "") override;

    [[nodiscard]] std::shared_ptr<SqlStatement> createStatement() override;

    bool beginTransaction(TransactionIsolation level = TransactionIsolation::ReadCommitted) override;
    bool commitTransaction() override;
    bool rollbackTransaction() override;
    bool createSavepoint(std::string_view name) override;
    bool rollbackToSavepoint(std::string_view name) override;
    bool releaseSavepoint(std::string_view name) override;

    [[nodiscard]] ConnectionMetadata metadata() const override;
    [[nodiscard]] std::string_view name() const override;
    [[nodiscard]] std::string_view version() const override;
    [[nodiscard]] bool ping() override;

    [[nodiscard]] const SqlError& lastError() const noexcept override;
    [[nodiscard]] bool supportsFeature(DriverFeature feature) const noexcept override;
    [[nodiscard]] std::vector<DriverFeature> getSupportedFeatures() const noexcept override;
    [[nodiscard]] int getMaxConnections() const noexcept override;
    [[nodiscard]] TransactionIsolation getDefaultTransactionIsolation() const noexcept override;
    [[nodiscard]] std::string_view getDefaultValidationQuery() const noexcept override;

    // Database Metadata API implementation
    [[nodiscard]] std::vector<SqlTable> getTables(
        std::string_view schema = "",
        SqlObjectType type = SqlObjectType::Table) const override;
    [[nodiscard]] bool tableExists(const SqlTable& table) const override;
    [[nodiscard]] bool indexExists(const SqlIndex& index) const override;
    [[nodiscard]] std::vector<std::string> getSchemas() const override;
    [[nodiscard]] bool schemaExists(std::string_view schemaName) const override;

    // Metadata operations (internal use)
    [[nodiscard]] SqlTable::Metadata getTableMetadata(const SqlTable& table) const override;
    [[nodiscard]] SqlIndex::Metadata getIndexMetadata(const SqlIndex& index) const override;

    /**
     * @brief Get the SQLite connection handle
     * @return The SQLite connection handle, or nullptr if not connected
     */
    [[nodiscard]] sqlite3* getHandle() const noexcept;

private:
    friend class SqliteStatement;
    void addActiveStatement(SqliteStatement* stmt);
    void removeActiveStatement(SqliteStatement* stmt);

    // Private implementation
    std::unique_ptr<SqliteDriverPrivate> d_ptr;

    // Helper methods
    [[nodiscard]] SqlError makeError(sqlite3* handle, std::string_view message, ErrorCode code);
    [[nodiscard]] bool executeSimpleQuery(std::string_view sql);
    [[nodiscard]] std::vector<SqlIndex::Metadata> getIndexesForTable(const SqlTable& table) const;
};

/**
 * @brief Create a new SQLite driver instance
 * @return A unique pointer to a SQLite driver
 */
[[nodiscard]] std::unique_ptr<SqlDriver> createSqliteDriver();

} // namespace database

#endif // DATABASE_SQLITE_DRIVER_H 