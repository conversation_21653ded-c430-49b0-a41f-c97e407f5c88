﻿#ifndef DATABASE_SQL_RESULT_H
#define DATABASE_SQL_RESULT_H

#include <string>
#include <string_view>

#include "variant.h"
#include "sql_types.h"
#include "sql_record.h"
#include "exception/sql_error.h"

namespace database {

// Forward declarations
class SqlStatement;
class SqlResultPrivate;

/**
 * @brief Class for database result sets
 *
 * This class provides a caching mechanism for result sets.
 * It is designed to work with both forward-only and scrollable result sets.
 * It uses a SqlStatement to fetch rows from the backend.
 */
class SqlResult {
public:
    /**
     * @brief Cursor position in the result set
     */
    enum Location {
        BeforeFirstRow = -1,  ///< Before the first row
        AfterLastRow = -2     ///< After the last row
    };

    /**
     * @brief Constructor
     * @param forwardOnly Whether the result set is forward-only
     */
    explicit SqlResult(bool forwardOnly = false);

    /**
     * @brief Destructor
     */
    ~SqlResult();

    // Prevent copying
    SqlResult(const SqlResult&) = delete;
    SqlResult& operator=(const SqlResult&) = delete;

    // Allow moving
    SqlResult(SqlResult&&) noexcept = default;
    SqlResult& operator=(SqlResult&&) noexcept = default;

    /**
     * @brief Initialize the result with a statement and metadata
     * @param statement Pointer to the statement (ownership not transferred)
     * @param metadata Record with column metadata
     * @param columnNames Optional column names (if not provided, extracted from metadata)
     */
    void initialize(SqlStatement* statement, const SqlRecord& metadata,
                    const std::vector<std::string>& columnNames = {});

    //----------------------------------------------------------------------
    // Result set navigation methods
    //----------------------------------------------------------------------

    // fetch(int row): 0-based, fetch(0) is the first row
    /**
     * @brief Move to a specific row in the result set
     * @param row The row index (0-based)
     * @return True if successful, false otherwise
     */
    bool fetch(int row);

    /**
     * @brief Move to the next row in the result set
     * @return True if successful, false if no more rows
     */
    bool next();

    /**
     * @brief Move to the previous row in the result set
     * @return True if successful, false if no more rows
     */
    bool previous();

    /**
     * @brief Move to the first row in the result set
     * @return True if successful, false if the result set is empty
     */
    bool first();

    /**
     * @brief Move to the last row in the result set
     * @return True if successful, false if the result set is empty
     */
    bool last();

    //----------------------------------------------------------------------
    // Data access methods
    //----------------------------------------------------------------------
    /**
     * @brief Get a value from the current row
     * @param index The column index (0-based)
     * @return The value
     */
    [[nodiscard]] Variant value(int index) const;

    /**
     * @brief Get a value from the current row
     * @param name The column name
     * @return The value
     */
    [[nodiscard]] Variant value(std::string_view name) const;

    /**
     * @brief Get the current row as a SqlRecord
     * @return A SqlRecord representing the current row
     */
    [[nodiscard]] SqlRecord record() const;

    //----------------------------------------------------------------------
    // Common methods
    //----------------------------------------------------------------------

    /**
     * @brief Check if the result set is empty
     * @return True if empty, false otherwise
     */
    [[nodiscard]] bool isEmpty() const;

    /**
     * @brief Check if the result set is active
     * @return True if active, false otherwise
     */
    [[nodiscard]] bool isActive() const;

    /**
     * @brief Get the number of rows in the result set
     * @return The row count, or -1 if unknown
     */
    [[nodiscard]] int rowCount() const;

    /**
     * @brief Get the current row index
     * @return The current row index, or a Location value
     */
    [[nodiscard]] int currentRow() const;

    /**
     * @brief Get the number of columns in the result set
     * @return The column count
     */
    [[nodiscard]] int columnCount() const;

    /**
     * @brief Get the column names
     * @return Vector of column names
     */
    [[nodiscard]] std::vector<std::string> columnNames() const;

    /**
     * @brief Get the last error that occurred
     * @return The last error
     */
    [[nodiscard]] const SqlError& lastError() const;

    /**
     * @brief Close the result set
     *
     * This method closes the result set and releases resources.
     * It removes itself from the parent statement's management.
     * After closing, the result set can still be used to access cached data,
     * but it cannot fetch new data from the database.
     *
     * @return True if successful, false otherwise
     */
    bool close();

protected:
    /**
     * @brief Initialize the result set
     * @param columnCount The number of columns
     */
    void init(int columnCount);

    /**
     * @brief Clean up the result set
     */
    void cleanup();

    /**
     * @brief Clear the cached values
     */
    void clearValues();

    /**
     * @brief Check if the result set is forward-only
     * @return True if forward-only, false otherwise
     */
    [[nodiscard]] bool isForwardOnly() const;

    /**
     * @brief Get the column index for a column name
     * @param name The column name
     * @return The column index, or -1 if not found
     */
    [[nodiscard]] int getColumnIndex(std::string_view name) const;

    /**
     * @brief Set the column names
     * @param names The column names
     */
    void setColumnNames(const std::vector<std::string>& names);

    /**
     * @brief Set the last error
     * @param message The error message
     * @param errorCode The error code
     * @param sqlState The SQL state
     */
    void setLastError(std::string_view message,
                      ErrorCode errorCode = ErrorCode::Unknown,
                      std::string_view sqlState = "");

    /**
     * @brief Set the last error
     * @param error The error
     */
    void setLastError(const SqlError& error);

private:
    /**
     * @brief Cache the next row
     * @return True if successful, false if no more rows
     */
    bool cacheNext();

    // Private implementation
    std::unique_ptr<SqlResultPrivate> d_ptr;

    // Cursor position
    int m_currentRow = BeforeFirstRow;

    // Column information
    std::vector<std::string> m_columnNames;
    std::unordered_map<std::string, int, StringViewHash, std::equal_to<>> m_columnIndexes;

    // Statement that provides data
    SqlStatement* m_statement = nullptr;

    // Error information
    SqlError m_lastError;

    // State
    bool m_closed = false;
    bool m_initialized = false;

    friend class SqlResultPrivate;
};

} // namespace database

#endif // DATABASE_SQL_RESULT_H
