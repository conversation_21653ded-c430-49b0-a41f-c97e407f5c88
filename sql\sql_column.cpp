﻿#include "sql_column.h"

#include <format>
#include <stdexcept>

#include "sql_table.h"
#include "sql_condition.h"

namespace database {

// Note: Constructors are now inline in the header file

const SqlTable* SqlColumn::table() const noexcept {
    return m_table;
}

std::string SqlColumn::qualifiedName() const {
    if (m_name.empty()) {
        return {};
    }

    if (hasTable()) {
        return std::format("{}.{}", m_table->qualifiedName(), m_name);
    }
    return m_name;
}

FieldType SqlColumn::type() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->type;
    }
    return FieldType::Unknown;
}

const std::string& SqlColumn::nativeTypeName() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->nativeTypeName;
    }

    static const std::string empty;
    return empty;
}

bool SqlColumn::isNullable() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->nullable;
    }
    return true; // Default assumption
}

std::optional<int> SqlColumn::size() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->size;
    }
    return std::nullopt;
}

std::optional<int> SqlColumn::precision() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->precision;
    }
    return std::nullopt;
}

std::optional<int> SqlColumn::scale() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->scale;
    }
    return std::nullopt;
}

const std::optional<std::string>& SqlColumn::defaultValue() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->defaultValue;
    }

    static const std::optional<std::string> empty;
    return empty;
}

const std::string& SqlColumn::comment() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->comment;
    }

    static const std::string empty;
    return empty;
}

int SqlColumn::position() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->position;
    }
    return 0;
}

bool SqlColumn::hasConstraint(SqlConstraintType type) const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->hasConstraint(type);
    }
    return false;
}

bool SqlColumn::isPrimaryKey() const {
    requireMetadataMode();
    return hasConstraint(SqlConstraintType::PrimaryKey);
}

bool SqlColumn::isUnique() const {
    requireMetadataMode();
    return hasConstraint(SqlConstraintType::Unique);
}

bool SqlColumn::isAutoIncrement() const {
    requireMetadataMode();
    return hasConstraint(SqlConstraintType::AutoIncrement);
}

const std::vector<SqlColumn::ConstraintInfo>& SqlColumn::constraints() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (m_metadata) {
        return m_metadata->constraints;
    }

    static const std::vector<ConstraintInfo> empty;
    return empty;
}

std::string SqlColumn::definitionSql() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    if (!m_metadata) {
        return "";
    }

    std::ostringstream sql;
    sql << m_name << " " << m_metadata->nativeTypeName;

    // Add size/precision/scale if applicable
    if (m_metadata->size.has_value()) {
        sql << "(" << m_metadata->size.value();
        if (m_metadata->scale.has_value()) {
            sql << "," << m_metadata->scale.value();
        }
        sql << ")";
    } else if (m_metadata->precision.has_value()) {
        sql << "(" << m_metadata->precision.value();
        if (m_metadata->scale.has_value()) {
            sql << "," << m_metadata->scale.value();
        }
        sql << ")";
    }

    // Add constraints
    if (!m_metadata->nullable) {
        sql << " NOT NULL";
    }

    if (m_metadata->defaultValue.has_value()) {
        sql << " DEFAULT " << m_metadata->defaultValue.value();
    }

    // Add other constraints
    for (const auto& constraint : m_metadata->constraints) {
        switch (constraint.type) {
        case SqlConstraintType::PrimaryKey:
            sql << " PRIMARY KEY";
            break;
        case SqlConstraintType::Unique:
            sql << " UNIQUE";
            break;
        case SqlConstraintType::AutoIncrement:
            sql << " AUTO_INCREMENT";
            break;
        case SqlConstraintType::Check:
            if (!constraint.definition.empty()) {
                sql << " CHECK (" << constraint.definition << ")";
            }
            break;
        default:
            break;
        }
    }

    return sql.str();
}

std::string SqlColumn::addColumnSql(const std::string& tableName) const {
    std::string table = tableName.empty() && m_table ? m_table->qualifiedName() : tableName;
    if (table.empty()) {
        return "";
    }

    return std::format("ALTER TABLE {} ADD COLUMN {}", table, definitionSql());
}

std::string SqlColumn::dropColumnSql(const std::string& tableName) const {
    std::string table = tableName.empty() && m_table ? m_table->qualifiedName() : tableName;
    if (table.empty() || m_name.empty()) {
        return "";
    }

    return std::format("ALTER TABLE {} DROP COLUMN {}", table, m_name);
}

SqlColumn& SqlColumn::reload() {
    invalidateCache();
    return *this;
}

SqlColumn& SqlColumn::preloadMetadata() {
    requireMetadataMode();
    ensureMetadataLoaded();
    return *this;
}

const SqlColumn::SqlColumnMetadata* SqlColumn::metadata() const {
    requireMetadataMode();
    ensureMetadataLoaded();
    return m_metadata.get();
}

void SqlColumn::setMetadata(const SqlColumnMetadata& metadata) {
    // This method can be called in both modes for internal use
    m_metadata = std::make_shared<SqlColumnMetadata>(metadata);
}

void SqlColumn::loadMetadata() const {
    if (!m_driver || m_name.empty() || m_mode != SqlObjectMode::Metadata) {
        return;
    }

    // TODO: Implement actual metadata loading from driver
    // For now, create empty metadata structure
    if (!m_metadata) {
        m_metadata = std::make_shared<SqlColumnMetadata>();
        m_metadata->name = m_name;
    }
}

void SqlColumn::invalidateCache() {
    // Only clear cache if in metadata mode
    if (m_mode == SqlObjectMode::Metadata) {
        m_metadata.reset();
    }
}

void SqlColumn::ensureMetadataLoaded() const {
    if (m_mode == SqlObjectMode::Metadata) {
        loadMetadata();
    }
}

std::string SqlColumn::toSql() const {
    if (isMetadataMode()) {
        // In metadata mode, check for alias in metadata
        if (m_metadata && !m_metadata->alias.empty()) {
            return std::format("{} AS {}", qualifiedName(), m_metadata->alias);
        }
    }
    return qualifiedName();
}

bool SqlColumn::setName(std::string_view name) {
    if (name.empty()) {
        return false;
    }
    m_name = name;
    if (isMetadataMode()) {
        invalidateCache();  // Clear cached metadata when name changes
    }
    return true;
}

bool SqlColumn::setTable(const SqlTable& table) {
    m_table = &table;
    return true;
}

bool SqlColumn::setDriver(SqlDriver* driver) {
    m_driver = driver;
    if (driver) {
        m_mode = SqlObjectMode::Metadata;  // Switch to metadata mode when driver is set
    }
    return true;
}

bool SqlColumn::setAlias(std::string_view alias) {
    if (isMetadataMode()) {
        ensureMetadataLoaded();
        if (m_metadata) {
            m_metadata->alias = alias;
            return true;
        }
    }
    // In builder mode, we could store alias separately, but for now just return true
    return true;
}

SqlColumn& SqlColumn::as(std::string_view alias) noexcept {
    setAlias(alias);
    return *this;
}

SqlCondition SqlColumn::eq(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::Equal, value);
}

SqlCondition SqlColumn::neq(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::NotEqual, value);
}

SqlCondition SqlColumn::lt(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::LessThan, value);
}

SqlCondition SqlColumn::lte(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::LessEqual, value);
}

SqlCondition SqlColumn::gt(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::GreaterThan, value);
}

SqlCondition SqlColumn::gte(const Variant& value) const {
    return SqlCondition(*this, SqlOperator::GreaterEqual, value);
}

SqlCondition SqlColumn::like(std::string_view pattern) const {
    return SqlCondition(*this, SqlOperator::Like, pattern);
}

SqlCondition SqlColumn::notLike(std::string_view pattern) const {
    return SqlCondition(*this, SqlOperator::NotLike, pattern);
}

SqlCondition SqlColumn::in(const std::vector<Variant>& values) const {
    return SqlCondition(*this, SqlOperator::In, values);
}

SqlCondition SqlColumn::notIn(const std::vector<Variant>& values) const {
    return SqlCondition(*this, SqlOperator::NotIn, values);
}

SqlCondition SqlColumn::between(const Variant& min, const Variant& max) const {
    // Create the vector with the right capacity to avoid reallocations
    std::vector<Variant> values;
    values.reserve(2);
    values.push_back(min);
    values.push_back(max);
    return SqlCondition(*this, SqlOperator::Between, values);
}

SqlCondition SqlColumn::isNull() const {
    return SqlCondition(*this, SqlOperator::IsNull);
}

SqlCondition SqlColumn::isNotNull() const {
    return SqlCondition(*this, SqlOperator::IsNotNull);
}

SqlCondition SqlColumn::condition(std::string_view op, const Variant& value) const {
    return SqlCondition(*this, op, value);
}

SqlCondition SqlColumn::condition(std::string_view op) const {
    return SqlCondition(*this, op);
}

SqlCondition SqlColumn::condition(std::string_view op, const std::vector<Variant>& values) const {
    return SqlCondition(*this, op, values);
}

std::strong_ordering SqlColumn::operator<=>(const SqlColumn& other) const noexcept {
    // Compare names first
    if (auto cmp = m_name <=> other.m_name; cmp != 0) {
        return cmp;
    }

    // Compare table presence
    if (hasTable() != other.hasTable()) {
        return hasTable() ? std::strong_ordering::greater : std::strong_ordering::less;
    }

    // Compare tables if both present
    if (hasTable() && other.hasTable()) {
        if (*m_table != *other.m_table) {
            // Since SqlTable doesn't have <=> yet, we'll use a simple comparison
            // This could be improved if SqlTable gets a <=> operator
            return m_table->name() < other.m_table->name() ?
                       std::strong_ordering::less : std::strong_ordering::greater;
        }
    }

    return m_metadata <=> other.m_metadata;
}

} // namespace database
